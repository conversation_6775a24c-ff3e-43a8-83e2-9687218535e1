import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios';
import useAuthStore from '../stores/authStore';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
const API_VERSION = '/api/v1';

// Create axios instance
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}${API_VERSION}`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const authHeader = useAuthStore.getState().getAuthHeader();
    
    if (authHeader) {
      config.headers.Authorization = authHeader;
    }
    
    // Add request ID for tracking
    config.headers['X-Request-ID'] = crypto.randomUUID();
    
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
      headers: config.headers,
      data: config.data,
    });
    
    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`✅ API Response: ${response.status}`, {
      url: response.config.url,
      data: response.data,
    });
    return response;
  },
  (error) => {
    console.error('❌ API Error:', {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      url: error.config?.url,
    });

    // Handle token expiration
    if (error.response?.status === 401) {
      const { logout } = useAuthStore.getState();
      logout();
      
      // Redirect to login page
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Generic API request function
export const apiRequest = async <T = any>(
  config: AxiosRequestConfig
): Promise<T> => {
  try {
    const response = await apiClient.request<T>(config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  meta: {
    timestamp: string;
    request_id: string;
    trace_id: string;
    version: string;
    status_code: number;
  };
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  meta: {
    timestamp: string;
    request_id: string;
    trace_id: string;
    version: string;
    status_code: number;
  };
}

// Application/Service types
export interface Application {
  id: string;
  name: string;
  description?: string;
  environment: {
    name: string;
    variables: Record<string, string>;
    secrets: string[];
    build_command?: string;
    start_command?: string;
    dockerfile_path?: string | null;
    root_directory?: string;
  };
  runtime_config: {
    service_type: 'WebService' | 'BackgroundWorker' | 'CronJob' | 'StaticSite';
    instance_type: string;
    region: string;
    plan?: string;
    instances?: number;
    memory_mb?: number;
    auto_scaling?: {
      enabled: boolean;
      min_instances: number;
      max_instances: number;
      target_cpu_percent: number;
      target_memory_percent: number;
      scale_up_cooldown: number;
      scale_down_cooldown: number;
    };
    health_check?: {
      enabled: boolean;
      path: string;
      port: number;
      interval: number;
      timeout: number;
      retries: number;
      grace_period: number;
    };
    networking?: {
      port: number;
      custom_domains: string[];
      ssl_enabled: boolean;
      internal_networking: boolean;
    };
  };
  status: 'Creating' | 'Running' | 'Stopped' | 'Failed' | 'Building' | 'Deploying' | 'Suspended';
  url?: string;
  created_at: string;
  updated_at: string;
  // Legacy fields for backward compatibility
  runtime?: string;
  region?: string;
  deployed?: string;
  repository?: {
    url: string;
    branch: string;
    provider: string;
  };
  hosting_tier?: string;
}

export interface CreateApplicationRequest {
  name: string;
  repository: {
    url: string;
    branch: string;
    provider: string;
  };
  runtime_config?: {
    runtime_type: string;
    version?: string;
    build_command?: string;
    start_command?: string;
  };
  environment?: Record<string, string>;
  hosting_tier?: string;
}

export interface CreateSimpleApplicationRequest {
  name: string;
  description?: string;
  deployment_mode?: 'Git' | 'Upload' | 'Docker';
  repository_url?: string;
  branch?: string;
  docker_image?: string;
  service_type: string;
  hosting_tier: string;
  custom_plan?: string;
  environment_variables?: Record<string, string>;
  build_command?: string;
  start_command?: string;
  region?: string;
  vultr_plan?: string;
}

// Application API functions
export const applicationApi = {
  // List all applications for the current user
  list: async (): Promise<Application[]> => {
    const response = await apiRequest<ApiResponse<Application[]>>({
      method: 'GET',
      url: '/applications',
    });
    return response.data;
  },

  // Get a specific application by ID
  get: async (id: string): Promise<Application> => {
    const response = await apiRequest<ApiResponse<Application>>({
      method: 'GET',
      url: `/applications/${id}`,
    });
    return response.data;
  },

  // Create a new application (full)
  create: async (data: CreateApplicationRequest): Promise<Application> => {
    const response = await apiRequest<ApiResponse<Application>>({
      method: 'POST',
      url: '/applications',
      data,
    });
    return response.data;
  },

  // Create a new application (simplified)
  createSimple: async (data: CreateSimpleApplicationRequest): Promise<Application> => {
    const response = await apiRequest<ApiResponse<Application>>({
      method: 'POST',
      url: '/applications/simple',
      data,
    });
    return response.data;
  },

  // Update an application
  update: async (id: string, data: Partial<CreateApplicationRequest>): Promise<Application> => {
    const response = await apiRequest<ApiResponse<Application>>({
      method: 'PUT',
      url: `/applications/${id}`,
      data,
    });
    return response.data;
  },

  // Delete an application
  delete: async (id: string): Promise<void> => {
    await apiRequest({
      method: 'DELETE',
      url: `/applications/${id}`,
    });
  },

  // Deploy an application
  deploy: async (id: string): Promise<any> => {
    const response = await apiRequest<ApiResponse<any>>({
      method: 'POST',
      url: `/applications/${id}/deploy`,
    });
    return response.data;
  },

  // Stop an application
  stop: async (id: string): Promise<any> => {
    const response = await apiRequest<ApiResponse<any>>({
      method: 'POST',
      url: `/applications/${id}/stop`,
    });
    return response.data;
  },

  // Get application stats
  getStats: async (): Promise<any> => {
    const response = await apiRequest<ApiResponse<any>>({
      method: 'GET',
      url: `/applications/stats`,
    });
    return response.data;
  },
};

// Hosting Plan types
export interface HostingPlan {
  tier: 'Shared' | 'Dedicated' | 'Enterprise';
  plan_name: string;
  description: string;
  features: string[];
  cpu_allocation?: number;
  memory_allocation?: number;
  storage_allocation?: number;
  bandwidth_limit?: number;
  max_concurrent_users?: number;
  price_per_hour: number;
  price_per_month: number;
  vultr_plan?: string;
  recommended_for: string[];
}

// Vultr Plan types (from backend)
export interface VultrPlan {
  id: string;
  vcpu_count: number;
  ram: number;
  disk: number;
  bandwidth: number;
  monthly_cost: number;
  type: string;
  locations: string[];
}

// Vultr/Compute API functions
export const computeApi = {
  // Get all Vultr plans
  getPlans: async (): Promise<VultrPlan[]> => {
    const response = await apiRequest<ApiResponse<VultrPlan[]>>({
      method: 'GET',
      url: '/compute/plans',
    });
    return response.data;
  },

  // Get all regions
  getRegions: async (): Promise<any[]> => {
    const response = await apiRequest<ApiResponse<any[]>>({
      method: 'GET',
      url: '/compute/regions',
    });
    return response.data;
  },

  // Get operating systems
  getOperatingSystems: async (): Promise<any[]> => {
    const response = await apiRequest<ApiResponse<any[]>>({
      method: 'GET',
      url: '/compute/os',
    });
    return response.data;
  },

  // Get servers
  getServers: async (): Promise<any[]> => {
    const response = await apiRequest<ApiResponse<any[]>>({
      method: 'GET',
      url: '/compute/servers',
    });
    return response.data;
  },

  // Get specific server
  getServer: async (id: string): Promise<any> => {
    const response = await apiRequest<ApiResponse<any>>({
      method: 'GET',
      url: `/compute/servers/${id}`,
    });
    return response.data;
  },
};

// Hosting Plans API functions (fallback to compute plans for now)
export const hostingPlansApi = {
  // Get all hosting plans (using Vultr plans as fallback)
  list: async (): Promise<HostingPlan[]> => {
    try {
      // Try to get hosting plans first
      const response = await apiRequest<ApiResponse<HostingPlan[]>>({
        method: 'GET',
        url: '/public/hosting/plans',
      });
      return response.data;
    } catch (error) {
      // Fallback to Vultr plans and transform them
      const vultrPlans = await computeApi.getPlans();
      return vultrPlans.map(plan => ({
        tier: 'Shared' as const,
        plan_name: plan.id,
        description: `${plan.vcpu_count} vCPU, ${plan.ram}MB RAM, ${plan.disk}GB Storage`,
        features: [
          `${plan.vcpu_count} vCPU`,
          `${plan.ram}MB RAM`,
          `${plan.disk}GB Storage`,
          `${plan.bandwidth}TB Bandwidth`
        ],
        cpu_allocation: plan.vcpu_count,
        memory_allocation: plan.ram,
        storage_allocation: plan.disk,
        bandwidth_limit: plan.bandwidth * 1024, // Convert TB to GB
        price_per_hour: plan.monthly_cost / (30 * 24),
        price_per_month: plan.monthly_cost,
        vultr_plan: plan.id,
        recommended_for: ['Web Applications', 'APIs', 'Small Websites']
      }));
    }
  },

  // Get recommended plan (simple logic for now)
  getRecommended: async (serviceType?: string, expectedTraffic?: string): Promise<HostingPlan> => {
    const plans = await hostingPlansApi.list();
    // Simple recommendation logic - return the first plan for now
    return plans[0] || {
      tier: 'Shared',
      plan_name: 'starter',
      description: 'Basic shared hosting plan',
      features: ['1 vCPU', '1GB RAM', '25GB Storage'],
      price_per_hour: 0.007,
      price_per_month: 5.00,
      recommended_for: ['Small websites', 'Personal projects']
    };
  },

  // Get specific plan by name
  get: async (planName: string): Promise<HostingPlan> => {
    const plans = await hostingPlansApi.list();
    const plan = plans.find(p => p.plan_name === planName);
    if (!plan) {
      throw new Error(`Plan ${planName} not found`);
    }
    return plan;
  },
};

// Export the configured axios instance
export default apiClient;
