import { config } from 'dotenv';
import { z } from 'zod';

// Load environment variables
config();

// Environment validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  PORT: z.string().transform(Number).default(3000),
  HOST: z.string().default('0.0.0.0'),
  
  // Database
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
  
  // Authentication
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  
  // Vultr API
  VULTR_API_KEY: z.string().min(1, 'VULTR_API_KEY is required'),
  VULTR_API_BASE_URL: z.string().url().default('https://api.vultr.com/v2'),
  
  // Logging
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
  LOG_PRETTY: z.string().transform(val => val === 'true').default(false),
  
  // Rate Limiting
  RATE_LIMIT_MAX: z.string().transform(Number).default(100),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default(60000),
  
  // CORS
  CORS_ORIGIN: z.string().default('http://localhost:3001'),
  CORS_CREDENTIALS: z.string().transform(val => val === 'true').default(true),
  
  // Monitoring
  JAEGER_ENDPOINT: z.string().url().optional(),
  METRICS_PORT: z.string().transform(Number).default(9090),
  
  // Circuit Breaker
  CIRCUIT_BREAKER_FAILURE_THRESHOLD: z.string().transform(Number).default(5),
  CIRCUIT_BREAKER_TIMEOUT_SECONDS: z.string().transform(Number).default(60),
});

// Validate environment variables
const env = envSchema.parse(process.env);

// Export configuration object
export const appConfig = {
  server: {
    port: env.PORT,
    host: env.HOST,
    environment: env.NODE_ENV,
  },
  database: {
    url: env.DATABASE_URL,
  },
  auth: {
    jwtSecret: env.JWT_SECRET,
    jwtExpiresIn: env.JWT_EXPIRES_IN,
  },
  vultr: {
    apiKey: env.VULTR_API_KEY,
    baseUrl: env.VULTR_API_BASE_URL,
  },
  logging: {
    level: env.LOG_LEVEL,
    pretty: env.LOG_PRETTY,
  },
  rateLimit: {
    max: env.RATE_LIMIT_MAX,
    window: env.RATE_LIMIT_WINDOW,
  },
  cors: {
    origin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
    credentials: env.CORS_CREDENTIALS,
  },
  monitoring: {
    jaegerEndpoint: env.JAEGER_ENDPOINT,
    metricsPort: env.METRICS_PORT,
  },
  circuitBreaker: {
    failureThreshold: env.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
    timeoutSeconds: env.CIRCUIT_BREAKER_TIMEOUT_SECONDS,
  },
} as const;

export type AppConfig = typeof appConfig;
