"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.applicationService = exports.ApplicationService = void 0;
const mongodb_1 = require("mongodb");
const connection_1 = require("../database/connection");
const models_1 = require("../models");
const logger_1 = require("../utils/logger");
class ApplicationService {
    applicationsCollection;
    constructor() {
        this.applicationsCollection = connection_1.databaseConnection.getCollection('applications');
    }
    async createApplication(userId, applicationData) {
        try {
            const existingApp = await this.applicationsCollection.findOne({
                user_id: userId,
                name: applicationData.name
            });
            if (existingApp) {
                throw new Error('Application with this name already exists');
            }
            const now = new Date();
            const newApplication = {
                user_id: userId,
                name: applicationData.name,
                description: applicationData.description,
                type: applicationData.type,
                status: models_1.ApplicationStatus.DRAFT,
                repository: applicationData.repository,
                environment: applicationData.environment,
                deployment: applicationData.deployment,
                resources: applicationData.resources || {},
                created_at: now,
                updated_at: now,
                stats: {
                    total_deployments: 0,
                    successful_deployments: 0,
                    failed_deployments: 0,
                    last_deployment_status: 'none',
                },
            };
            const result = await this.applicationsCollection.insertOne(newApplication);
            const createdApplication = await this.applicationsCollection.findOne({ _id: result.insertedId });
            if (!createdApplication) {
                throw new Error('Failed to create application');
            }
            logger_1.logger.info(`Application created: ${applicationData.name} for user: ${userId}`);
            return (0, models_1.toApplicationResponse)(createdApplication);
        }
        catch (error) {
            logger_1.logger.error('Create application error:', error);
            throw error;
        }
    }
    async getApplicationById(applicationId, userId) {
        try {
            const query = { _id: new mongodb_1.ObjectId(applicationId) };
            if (userId) {
                query.user_id = userId;
            }
            const application = await this.applicationsCollection.findOne(query);
            if (!application) {
                throw new Error('Application not found');
            }
            return (0, models_1.toApplicationResponse)(application);
        }
        catch (error) {
            logger_1.logger.error('Get application by ID error:', error);
            throw error;
        }
    }
    async getApplicationsByUser(userId, options = {}) {
        try {
            const { page = 1, limit = 10, sort = 'created_at', order = 'desc' } = options;
            const skip = (page - 1) * limit;
            const sortObj = {};
            sortObj[sort] = order === 'asc' ? 1 : -1;
            const applications = await this.applicationsCollection
                .find({ user_id: userId })
                .sort(sortObj)
                .skip(skip)
                .limit(limit)
                .toArray();
            const total = await this.applicationsCollection.countDocuments({ user_id: userId });
            const pages = Math.ceil(total / limit);
            return {
                data: applications.map(models_1.toApplicationResponse),
                pagination: {
                    page,
                    limit,
                    total,
                    pages,
                    has_next: page < pages,
                    has_prev: page > 1,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Get applications by user error:', error);
            throw error;
        }
    }
    async updateApplication(applicationId, userId, updateData) {
        try {
            const existingApp = await this.applicationsCollection.findOne({
                _id: new mongodb_1.ObjectId(applicationId),
                user_id: userId
            });
            if (!existingApp) {
                throw new Error('Application not found');
            }
            if (updateData.name && updateData.name !== existingApp.name) {
                const nameConflict = await this.applicationsCollection.findOne({
                    user_id: userId,
                    name: updateData.name,
                    _id: { $ne: new mongodb_1.ObjectId(applicationId) }
                });
                if (nameConflict) {
                    throw new Error('Application with this name already exists');
                }
            }
            const updateFields = {
                ...updateData,
                updated_at: new Date(),
            };
            const result = await this.applicationsCollection.updateOne({ _id: new mongodb_1.ObjectId(applicationId), user_id: userId }, { $set: updateFields });
            if (result.matchedCount === 0) {
                throw new Error('Application not found');
            }
            const updatedApplication = await this.applicationsCollection.findOne({
                _id: new mongodb_1.ObjectId(applicationId)
            });
            if (!updatedApplication) {
                throw new Error('Failed to fetch updated application');
            }
            logger_1.logger.info(`Application updated: ${applicationId} for user: ${userId}`);
            return (0, models_1.toApplicationResponse)(updatedApplication);
        }
        catch (error) {
            logger_1.logger.error('Update application error:', error);
            throw error;
        }
    }
    async deleteApplication(applicationId, userId) {
        try {
            const result = await this.applicationsCollection.deleteOne({
                _id: new mongodb_1.ObjectId(applicationId),
                user_id: userId
            });
            if (result.deletedCount === 0) {
                throw new Error('Application not found');
            }
            logger_1.logger.info(`Application deleted: ${applicationId} for user: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error('Delete application error:', error);
            throw error;
        }
    }
    async updateApplicationStatus(applicationId, status, userId) {
        try {
            const query = { _id: new mongodb_1.ObjectId(applicationId) };
            if (userId) {
                query.user_id = userId;
            }
            const result = await this.applicationsCollection.updateOne(query, {
                $set: {
                    status,
                    updated_at: new Date()
                }
            });
            if (result.matchedCount === 0) {
                throw new Error('Application not found');
            }
            const updatedApplication = await this.applicationsCollection.findOne({
                _id: new mongodb_1.ObjectId(applicationId)
            });
            if (!updatedApplication) {
                throw new Error('Failed to fetch updated application');
            }
            logger_1.logger.info(`Application status updated: ${applicationId} to ${status}`);
            return (0, models_1.toApplicationResponse)(updatedApplication);
        }
        catch (error) {
            logger_1.logger.error('Update application status error:', error);
            throw error;
        }
    }
    async getApplicationStats(userId) {
        try {
            const pipeline = [
                { $match: { user_id: userId } },
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ];
            const statusCounts = await this.applicationsCollection.aggregate(pipeline).toArray();
            const stats = {
                total: 0,
                draft: 0,
                building: 0,
                deployed: 0,
                failed: 0,
                stopped: 0,
                suspended: 0,
            };
            statusCounts.forEach(item => {
                stats.total += item['count'];
                stats[item['_id']] = item['count'];
            });
            return stats;
        }
        catch (error) {
            logger_1.logger.error('Get application stats error:', error);
            throw error;
        }
    }
}
exports.ApplicationService = ApplicationService;
exports.applicationService = new ApplicationService();
//# sourceMappingURL=application.js.map