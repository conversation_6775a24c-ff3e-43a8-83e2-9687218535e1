import { ObjectId } from 'mongodb';
import { z } from 'zod';

// Application status enum
export enum ApplicationStatus {
  DRAFT = 'draft',
  BUILDING = 'building',
  DEPLOYED = 'deployed',
  FAILED = 'failed',
  STOPPED = 'stopped',
  SUSPENDED = 'suspended',
}

// Application type enum
export enum ApplicationType {
  WEB_SERVICE = 'web_service',
  STATIC_SITE = 'static_site',
  BACKGROUND_WORKER = 'background_worker',
  CRON_JOB = 'cron_job',
  PRIVATE_SERVICE = 'private_service',
}

// Environment configuration
export interface EnvironmentConfig {
  name: string;
  variables: Record<string, string>;
  secrets: string[];
  build_command?: string;
  start_command?: string;
  dockerfile_path?: string;
  root_directory?: string;
  port?: number;
  health_check_path?: string;
}

// Deployment configuration
export interface DeploymentConfig {
  auto_deploy: boolean;
  branch: string;
  build_command?: string;
  start_command?: string;
  pre_deploy_command?: string;
  post_deploy_command?: string;
  dockerfile_path?: string;
  root_directory?: string;
}

// Resource limits
export interface ResourceLimits {
  cpu_shares?: number;
  memory_limit?: string;
  disk_limit?: string;
  bandwidth_limit?: string;
}

// Application interface for database
export interface Application {
  _id?: ObjectId;
  id?: string;
  user_id: string;
  name: string;
  description?: string;
  type: ApplicationType;
  status: ApplicationStatus;
  
  // Source configuration
  repository?: {
    url: string;
    branch: string;
    private: boolean;
    access_token?: string;
  };
  
  // Environment and deployment
  environment: EnvironmentConfig;
  deployment: DeploymentConfig;
  
  // Resource allocation
  resources: ResourceLimits;
  
  // Server assignment
  server_id?: string;
  domain?: string;
  subdomain?: string;
  
  // Metadata
  last_deployed_at?: Date;
  last_build_log?: string;
  build_duration?: number;
  created_at: Date;
  updated_at: Date;
  
  // Statistics
  stats?: {
    total_deployments: number;
    successful_deployments: number;
    failed_deployments: number;
    last_deployment_status: string;
  };
}

// Application response interface
export interface ApplicationResponse {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  type: ApplicationType;
  status: ApplicationStatus;
  repository?: {
    url: string;
    branch: string;
    private: boolean;
  };
  environment: EnvironmentConfig;
  deployment: DeploymentConfig;
  resources: ResourceLimits;
  server_id?: string;
  domain?: string;
  subdomain?: string;
  last_deployed_at?: string;
  created_at: string;
  updated_at: string;
  stats?: {
    total_deployments: number;
    successful_deployments: number;
    failed_deployments: number;
    last_deployment_status: string;
  };
}

// Validation schemas
export const createApplicationSchema = z.object({
  name: z.string().min(1, 'Application name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  type: z.nativeEnum(ApplicationType),
  
  repository: z.object({
    url: z.string().url('Invalid repository URL'),
    branch: z.string().min(1, 'Branch is required').default('main'),
    private: z.boolean().default(false),
    access_token: z.string().optional(),
  }).optional(),
  
  environment: z.object({
    name: z.string().default('production'),
    variables: z.record(z.string()).default({}),
    secrets: z.array(z.string()).default([]),
    build_command: z.string().optional(),
    start_command: z.string().optional(),
    dockerfile_path: z.string().optional(),
    root_directory: z.string().default('./'),
    port: z.number().min(1).max(65535).optional(),
    health_check_path: z.string().optional(),
  }),
  
  deployment: z.object({
    auto_deploy: z.boolean().default(true),
    branch: z.string().default('main'),
    build_command: z.string().optional(),
    start_command: z.string().optional(),
    pre_deploy_command: z.string().optional(),
    post_deploy_command: z.string().optional(),
    dockerfile_path: z.string().optional(),
    root_directory: z.string().default('./'),
  }),
  
  resources: z.object({
    cpu_shares: z.number().min(1).max(1024).optional(),
    memory_limit: z.string().optional(),
    disk_limit: z.string().optional(),
    bandwidth_limit: z.string().optional(),
  }).default({}),
});

export const updateApplicationSchema = createApplicationSchema.partial();

// Type exports for request/response
export type CreateApplicationRequest = z.infer<typeof createApplicationSchema>;
export type UpdateApplicationRequest = z.infer<typeof updateApplicationSchema>;

// Helper function to convert Application to ApplicationResponse
export function toApplicationResponse(application: Application): ApplicationResponse {
  // @ts-ignore - exactOptionalPropertyTypes compatibility
  return {
    id: application._id?.toString() || application.id || '',
    user_id: application.user_id,
    name: application.name,
    description: application.description,
    type: application.type,
    status: application.status,
    repository: application.repository ? {
      url: application.repository.url,
      branch: application.repository.branch,
      private: application.repository.private,
    } : undefined,
    environment: application.environment,
    deployment: application.deployment,
    resources: application.resources,
    server_id: application.server_id,
    domain: application.domain,
    subdomain: application.subdomain,
    last_deployed_at: application.last_deployed_at?.toISOString(),
    created_at: application.created_at.toISOString(),
    updated_at: application.updated_at.toISOString(),
    stats: application.stats,
  };
}
