"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestLogger = exports.logger = void 0;
const pino_1 = __importDefault(require("pino"));
const config_1 = require("../config");
const loggerOptions = {
    level: config_1.appConfig.logging.level,
    formatters: {
        level: (label) => {
            return { level: label };
        },
    },
    timestamp: pino_1.default.stdTimeFunctions.isoTime,
    base: {
        env: config_1.appConfig.server.environment,
        service: 'achidas-backend',
    },
};
if (config_1.appConfig.logging.pretty) {
    loggerOptions.transport = {
        target: 'pino-pretty',
        options: {
            colorize: true,
            translateTime: 'SYS:standard',
            ignore: 'pid,hostname',
        },
    };
}
exports.logger = (0, pino_1.default)(loggerOptions);
exports.requestLogger = {
    logger: exports.logger,
    serializers: {
        req: pino_1.default.stdSerializers.req,
        res: pino_1.default.stdSerializers.res,
    },
};
//# sourceMappingURL=logger.js.map