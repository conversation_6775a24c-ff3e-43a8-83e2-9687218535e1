import { FastifyRequest, FastifyReply } from 'fastify';
export declare function getServersController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getServerController(request: FastifyRequest<{
    Params: {
        id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function createServerController(request: FastifyRequest<{
    Body: any;
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function deleteServerController(request: FastifyRequest<{
    Params: {
        id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function startServerController(request: FastifyRequest<{
    Params: {
        id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function stopServerController(request: FastifyRequest<{
    Params: {
        id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function rebootServerController(request: FastifyRequest<{
    Params: {
        id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getPlansController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getRegionsController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getBillingController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getAccountInfoController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getOperatingSystemsController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getBlockStorageController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function createBlockStorageController(request: FastifyRequest<{
    Body: any;
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function deleteBlockStorageController(request: FastifyRequest<{
    Params: {
        id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function attachBlockStorageController(request: FastifyRequest<{
    Params: {
        id: string;
    };
    Body: {
        instance_id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function detachBlockStorageController(request: FastifyRequest<{
    Params: {
        id: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getSSHKeysController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getSnapshotsController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getLoadBalancersController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
//# sourceMappingURL=vultr.controller.d.ts.map