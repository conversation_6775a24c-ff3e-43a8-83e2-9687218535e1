import { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import { appConfig } from '../config';

export async function registerCors(fastify: FastifyInstance): Promise<void> {
  await fastify.register(cors, {
    origin: appConfig.cors.origin,
    credentials: appConfig.cors.credentials,
    methods: appConfig.cors.methods,
    allowedHeaders: appConfig.cors.allowedHeaders,
  });
}
