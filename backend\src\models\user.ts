import { ObjectId } from 'mongodb';
import { z } from 'zod';

// User status enum
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

// User role enum
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

// User interface for database
export interface User {
  _id?: ObjectId;
  id?: string;
  email: string;
  username: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  company?: string;
  role: UserRole;
  status: UserStatus;
  email_verified: boolean;
  phone?: string;
  avatar_url?: string;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
  metadata?: {
    login_attempts: number;
    last_login_ip?: string;
    preferences?: Record<string, any>;
  };
}

// User response interface (without sensitive data)
export interface UserResponse {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  company?: string | undefined;
  role: UserRole;
  status: UserStatus;
  email_verified: boolean;
  phone?: string | undefined;
  avatar_url?: string | undefined;
  last_login?: string | undefined;
  created_at: string;
  updated_at: string;
}

// Validation schemas
export const createUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  username: z.string().min(3, 'Username must be at least 3 characters').max(30, 'Username must be less than 30 characters'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  first_name: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  company: z.string().max(100, 'Company name must be less than 100 characters').optional(),
  phone: z.string().optional(),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

export const updateUserSchema = z.object({
  first_name: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters').optional(),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters').optional(),
  company: z.string().max(100, 'Company name must be less than 100 characters').optional(),
  phone: z.string().optional(),
  avatar_url: z.string().url('Invalid URL format').optional(),
});

export const changePasswordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string().min(8, 'New password must be at least 8 characters'),
});

// Type exports for request/response
export type CreateUserRequest = z.infer<typeof createUserSchema>;
export type LoginRequest = z.infer<typeof loginSchema>;
export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type ChangePasswordRequest = z.infer<typeof changePasswordSchema>;

// Helper function to convert User to UserResponse
export function toUserResponse(user: User): UserResponse {
  return {
    id: user._id?.toString() || user.id || '',
    email: user.email,
    username: user.username,
    first_name: user.first_name,
    last_name: user.last_name,
    company: user.company,
    role: user.role,
    status: user.status,
    email_verified: user.email_verified,
    phone: user.phone,
    avatar_url: user.avatar_url,
    last_login: user.last_login?.toISOString(),
    created_at: user.created_at.toISOString(),
    updated_at: user.updated_at.toISOString(),
  };
}
