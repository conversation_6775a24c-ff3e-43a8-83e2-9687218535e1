import { FastifyReply } from 'fastify';
import { v4 as uuidv4 } from 'uuid';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta: {
    timestamp: string;
    request_id: string;
    trace_id: string;
    version: string;
    status_code: number;
  };
}

export interface ResponseOptions {
  requestId?: string;
  traceId?: string;
  version?: string;
}

export class ResponseHelper {
  static success<T>(
    reply: FastifyReply,
    data: T,
    statusCode: number = 200,
    options: ResponseOptions = {}
  ): FastifyReply {
    const response: ApiResponse<T> = {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: options.requestId || uuidv4(),
        trace_id: options.traceId || uuidv4(),
        version: options.version || '1.0.0',
        status_code: statusCode,
      },
    };

    return reply.status(statusCode).send(response);
  }

  static error(
    reply: FastifyReply,
    code: string,
    message: string,
    statusCode: number = 400,
    details?: any,
    options: ResponseOptions = {}
  ): FastifyReply {
    const response: ApiResponse = {
      success: false,
      error: {
        code,
        message,
        details,
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: options.requestId || uuidv4(),
        trace_id: options.traceId || uuidv4(),
        version: options.version || '1.0.0',
        status_code: statusCode,
      },
    };

    return reply.status(statusCode).send(response);
  }

  static validationError(
    reply: FastifyReply,
    message: string,
    details?: any,
    options: ResponseOptions = {}
  ): FastifyReply {
    return this.error(reply, 'VALIDATION_ERROR', message, 422, details, options);
  }

  static notFound(
    reply: FastifyReply,
    message: string = 'Resource not found',
    options: ResponseOptions = {}
  ): FastifyReply {
    return this.error(reply, 'NOT_FOUND', message, 404, undefined, options);
  }

  static unauthorized(
    reply: FastifyReply,
    message: string = 'Unauthorized',
    options: ResponseOptions = {}
  ): FastifyReply {
    return this.error(reply, 'UNAUTHORIZED', message, 401, undefined, options);
  }

  static forbidden(
    reply: FastifyReply,
    message: string = 'Forbidden',
    options: ResponseOptions = {}
  ): FastifyReply {
    return this.error(reply, 'FORBIDDEN', message, 403, undefined, options);
  }

  static internalError(
    reply: FastifyReply,
    message: string = 'Internal server error',
    details?: any,
    options: ResponseOptions = {}
  ): FastifyReply {
    return this.error(reply, 'INTERNAL_ERROR', message, 500, details, options);
  }
}
