import { FastifyInstance } from 'fastify';
import { authRoutes } from './auth.routes';
import { applicationRoutes } from './application.routes';
import { vultrRoutes } from './vultr.routes';
import { hostingRoutes } from './hosting.routes';
import { serverRoutes } from './server.routes';
import { ResponseHelper } from '../utils/response';

export async function registerRoutes(fastify: FastifyInstance): Promise<void> {
  // Health check route
  fastify.get('/health', async (_request, reply) => {
    return ResponseHelper.success(reply, {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['npm_package_version'] || '1.0.0',
    });
  });

  // API version prefix
  fastify.register(async function (fastify) {
    // Register all route modules with v1 prefix
    await fastify.register(authRoutes, { prefix: '/auth' });
    await fastify.register(applicationRoutes, { prefix: '/applications' });
    await fastify.register(vultrRoutes, { prefix: '/compute' }); // Hide Vultr from routes
    await fastify.register(hostingRoutes, { prefix: '/hosting' });
    await fastify.register(serverRoutes, { prefix: '/servers' });
  }, { prefix: '/api/v1' });

  // 404 handler for unmatched routes
  fastify.setNotFoundHandler(async (request, reply) => {
    return ResponseHelper.notFound(reply, `Route ${request.method} ${request.url} not found`);
  });
}
