import { logger } from '../utils/logger';
import { execAsync } from '../utils/exec';

export interface SecurityAlert {
  id: string;
  type: 'unauthorized_access' | 'resource_abuse' | 'file_permission' | 'process_violation' | 'network_anomaly';
  severity: 'low' | 'medium' | 'high' | 'critical';
  user_id: string;
  username: string;
  description: string;
  details: Record<string, any>;
  timestamp: string;
  resolved: boolean;
  actions_taken: string[];
}

export interface UserIsolationStatus {
  user_id: string;
  username: string;
  linux_username: string;
  isolation_status: 'secure' | 'warning' | 'compromised';
  file_permissions_ok: boolean;
  process_limits_ok: boolean;
  network_restrictions_ok: boolean;
  resource_usage: {
    cpu_percent: number;
    memory_percent: number;
    disk_usage_mb: number;
    network_usage_mb: number;
  };
  last_checked: string;
  violations: string[];
}

export interface SecurityMetrics {
  total_users: number;
  secure_users: number;
  warning_users: number;
  compromised_users: number;
  active_alerts: number;
  resolved_alerts_24h: number;
  system_health_score: number;
}

class SecurityMonitoringService {
  private readonly SHARED_SERVER_IP = '*************';
  private readonly alerts: SecurityAlert[] = [];

  // Monitor user isolation security
  async monitorUserIsolation(): Promise<UserIsolationStatus[]> {
    try {
      logger.info('Starting user isolation security monitoring');
      
      const users = await this.getSharedHostingUsers();
      const statuses: UserIsolationStatus[] = [];

      for (const user of users) {
        const status = await this.checkUserIsolation(user);
        statuses.push(status);

        // Generate alerts for security violations
        if (status.isolation_status !== 'secure') {
          await this.generateSecurityAlert(user, status);
        }
      }

      logger.info(`Monitored ${statuses.length} users for security violations`);
      return statuses;
    } catch (error) {
      logger.error('Failed to monitor user isolation:', error);
      throw error;
    }
  }

  // Check individual user isolation status
  private async checkUserIsolation(user: any): Promise<UserIsolationStatus> {
    try {
      const filePermissionsOk = await this.checkFilePermissions(user.linux_username);
      const processLimitsOk = await this.checkProcessLimits(user.linux_username);
      const networkRestrictionsOk = await this.checkNetworkRestrictions(user.linux_username);
      const resourceUsage = await this.getUserResourceUsage(user.linux_username);

      const violations: string[] = [];
      if (!filePermissionsOk) violations.push('File permissions compromised');
      if (!processLimitsOk) violations.push('Process limits exceeded');
      if (!networkRestrictionsOk) violations.push('Network restrictions violated');

      let isolationStatus: 'secure' | 'warning' | 'compromised' = 'secure';
      if (violations.length > 0) {
        isolationStatus = violations.length >= 2 ? 'compromised' : 'warning';
      }

      return {
        user_id: user.user_id,
        username: user.username,
        linux_username: user.linux_username,
        isolation_status: isolationStatus,
        file_permissions_ok: filePermissionsOk,
        process_limits_ok: processLimitsOk,
        network_restrictions_ok: networkRestrictionsOk,
        resource_usage: resourceUsage,
        last_checked: new Date().toISOString(),
        violations
      };
    } catch (error) {
      logger.error(`Failed to check isolation for user ${user.username}:`, error);
      throw error;
    }
  }

  // Check file permissions for user isolation
  private async checkFilePermissions(linuxUsername: string): Promise<boolean> {
    try {
      const homeDir = `/var/www/user_${linuxUsername}`;
      
      // Check home directory permissions (should be 700)
      const homeDirPerms = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "stat -c '%a' ${homeDir}"`);
      if (homeDirPerms.stdout.trim() !== '700') {
        logger.warn(`Home directory permissions incorrect for ${linuxUsername}: ${homeDirPerms.stdout.trim()}`);
        return false;
      }

      // Check if user can access other users' directories
      const accessTest = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "sudo -u ${linuxUsername} ls /var/www/ | wc -l"`).catch(() => null);
      if (accessTest && parseInt(accessTest.stdout.trim()) > 1) {
        logger.warn(`User ${linuxUsername} can access other user directories`);
        return false;
      }

      return true;
    } catch (error) {
      logger.error(`Failed to check file permissions for ${linuxUsername}:`, error);
      return false;
    }
  }

  // Check process limits
  private async checkProcessLimits(linuxUsername: string): Promise<boolean> {
    try {
      // Check if systemd slice is active
      const sliceStatus = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "systemctl is-active user-${linuxUsername}.slice"`);
      if (sliceStatus.stdout.trim() !== 'active') {
        logger.warn(`Systemd slice not active for ${linuxUsername}`);
        return false;
      }

      // Check current resource usage against limits
      const cpuUsage = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "systemctl show user-${linuxUsername}.slice --property=CPUUsageNSec"`);
      const memoryUsage = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "systemctl show user-${linuxUsername}.slice --property=MemoryCurrent"`);

      // Basic validation that limits are being enforced
      return cpuUsage.stdout.includes('CPUUsageNSec=') && memoryUsage.stdout.includes('MemoryCurrent=');
    } catch (error) {
      logger.error(`Failed to check process limits for ${linuxUsername}:`, error);
      return false;
    }
  }

  // Check network restrictions
  private async checkNetworkRestrictions(linuxUsername: string): Promise<boolean> {
    try {
      // Check if traffic control rules are active
      const tcRules = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "tc qdisc show dev eth0 | grep ${linuxUsername}"`).catch(() => null);
      
      // Check for any suspicious network connections
      const netConnections = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "netstat -tulpn | grep ${linuxUsername}"`).catch(() => null);
      
      return true; // Basic implementation - can be enhanced
    } catch (error) {
      logger.error(`Failed to check network restrictions for ${linuxUsername}:`, error);
      return false;
    }
  }

  // Get user resource usage
  private async getUserResourceUsage(linuxUsername: string): Promise<any> {
    try {
      // Get CPU usage for user processes
      const cpuUsage = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "ps -u ${linuxUsername} -o %cpu --no-headers | awk '{sum += $1} END {print sum}'"`).catch(() => ({ stdout: '0' }));
      
      // Get memory usage for user processes
      const memoryUsage = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "ps -u ${linuxUsername} -o %mem --no-headers | awk '{sum += $1} END {print sum}'"`).catch(() => ({ stdout: '0' }));
      
      // Get disk usage for user directory
      const diskUsage = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "du -sm /var/www/user_${linuxUsername} | cut -f1"`).catch(() => ({ stdout: '0' }));
      
      return {
        cpu_percent: parseFloat(cpuUsage.stdout.trim()) || 0,
        memory_percent: parseFloat(memoryUsage.stdout.trim()) || 0,
        disk_usage_mb: parseInt(diskUsage.stdout.trim()) || 0,
        network_usage_mb: 0 // Placeholder - would need more complex monitoring
      };
    } catch (error) {
      logger.error(`Failed to get resource usage for ${linuxUsername}:`, error);
      return { cpu_percent: 0, memory_percent: 0, disk_usage_mb: 0, network_usage_mb: 0 };
    }
  }

  // Generate security alert
  private async generateSecurityAlert(user: any, status: UserIsolationStatus): Promise<void> {
    const alert: SecurityAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: status.isolation_status === 'compromised' ? 'unauthorized_access' : 'file_permission',
      severity: status.isolation_status === 'compromised' ? 'high' : 'medium',
      user_id: user.user_id,
      username: user.username,
      description: `User isolation ${status.isolation_status} detected`,
      details: {
        violations: status.violations,
        file_permissions_ok: status.file_permissions_ok,
        process_limits_ok: status.process_limits_ok,
        network_restrictions_ok: status.network_restrictions_ok,
        resource_usage: status.resource_usage
      },
      timestamp: new Date().toISOString(),
      resolved: false,
      actions_taken: []
    };

    this.alerts.push(alert);
    logger.warn(`Security alert generated: ${alert.id} for user ${user.username}`);

    // Auto-remediation for critical issues
    if (alert.severity === 'high' || alert.severity === 'critical') {
      await this.autoRemediate(alert);
    }
  }

  // Auto-remediation for security issues
  private async autoRemediate(alert: SecurityAlert): Promise<void> {
    try {
      const actions: string[] = [];

      if (alert.type === 'unauthorized_access' || alert.type === 'file_permission') {
        // Fix file permissions
        await execAsync(`ssh root@${this.SHARED_SERVER_IP} "chmod 700 /var/www/user_${alert.username}"`);
        await execAsync(`ssh root@${this.SHARED_SERVER_IP} "chown -R ${alert.username}:${alert.username} /var/www/user_${alert.username}"`);
        actions.push('Fixed file permissions');
      }

      if (alert.type === 'resource_abuse') {
        // Restart systemd slice to enforce limits
        await execAsync(`ssh root@${this.SHARED_SERVER_IP} "systemctl restart user-${alert.username}.slice"`);
        actions.push('Restarted resource limits');
      }

      alert.actions_taken = actions;
      logger.info(`Auto-remediation completed for alert ${alert.id}: ${actions.join(', ')}`);
    } catch (error) {
      logger.error(`Failed to auto-remediate alert ${alert.id}:`, error);
    }
  }

  // Get shared hosting users (placeholder - would integrate with user management)
  private async getSharedHostingUsers(): Promise<any[]> {
    try {
      // Get list of users from the server
      const userList = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "ls /var/www/ | grep user_"`);
      const userDirs = userList.stdout.trim().split('\n').filter(dir => dir.startsWith('user_'));
      
      return userDirs.map(dir => {
        const username = dir.replace('user_', '');
        return {
          user_id: `user_${username}`,
          username: username,
          linux_username: username
        };
      });
    } catch (error) {
      logger.error('Failed to get shared hosting users:', error);
      return [];
    }
  }

  // Get security metrics
  async getSecurityMetrics(): Promise<SecurityMetrics> {
    try {
      const statuses = await this.monitorUserIsolation();
      const activeAlerts = this.alerts.filter(alert => !alert.resolved).length;
      const resolved24h = this.alerts.filter(alert => 
        alert.resolved && 
        new Date(alert.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)
      ).length;

      const secureUsers = statuses.filter(s => s.isolation_status === 'secure').length;
      const warningUsers = statuses.filter(s => s.isolation_status === 'warning').length;
      const compromisedUsers = statuses.filter(s => s.isolation_status === 'compromised').length;

      const healthScore = statuses.length > 0 ? (secureUsers / statuses.length) * 100 : 100;

      return {
        total_users: statuses.length,
        secure_users: secureUsers,
        warning_users: warningUsers,
        compromised_users: compromisedUsers,
        active_alerts: activeAlerts,
        resolved_alerts_24h: resolved24h,
        system_health_score: Math.round(healthScore)
      };
    } catch (error) {
      logger.error('Failed to get security metrics:', error);
      throw error;
    }
  }

  // Get all alerts
  getAlerts(resolved?: boolean): SecurityAlert[] {
    if (resolved !== undefined) {
      return this.alerts.filter(alert => alert.resolved === resolved);
    }
    return this.alerts;
  }

  // Resolve alert
  async resolveAlert(alertId: string, resolution: string): Promise<void> {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      alert.actions_taken.push(`Manually resolved: ${resolution}`);
      logger.info(`Alert ${alertId} resolved: ${resolution}`);
    }
  }
}

// Singleton instance
let securityMonitoringService: SecurityMonitoringService;

export function getSecurityMonitoringService(): SecurityMonitoringService {
  if (!securityMonitoringService) {
    securityMonitoringService = new SecurityMonitoringService();
  }
  return securityMonitoringService;
}
