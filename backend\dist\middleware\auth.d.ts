import { FastifyRequest, FastifyReply } from 'fastify';
import { JwtPayload } from '../models';
declare module 'fastify' {
    interface FastifyRequest {
        user?: JwtPayload;
    }
}
export declare function authMiddleware(request: FastifyRequest, reply: FastifyReply): Promise<void>;
export declare function adminMiddleware(request: FastifyRequest, reply: FastifyReply): Promise<void>;
export declare function superAdminMiddleware(request: FastifyRequest, reply: FastifyReply): Promise<void>;
export declare function optionalAuthMiddleware(request: FastifyRequest, _reply: FastifyReply): Promise<void>;
export declare function createOwnershipMiddleware(userIdParam?: string): (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
export declare function createUserRateLimitMiddleware(maxRequests?: number, windowMs?: number): (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
//# sourceMappingURL=auth.d.ts.map