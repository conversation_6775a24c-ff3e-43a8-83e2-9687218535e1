import { ObjectId } from 'mongodb';
import { z } from 'zod';

// Server status enum
export enum ServerStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  INSTALLING = 'installing',
  STOPPED = 'stopped',
  MAINTENANCE = 'maintenance',
}

// Server type enum
export enum ServerType {
  SHARED = 'shared',
  DEDICATED = 'dedicated',
  ENTERPRISE = 'enterprise',
}

// Server interface for database
export interface Server {
  _id?: ObjectId;
  id?: string;
  vultr_id: string;
  name: string;
  label: string;
  hostname?: string;
  type: ServerType;
  status: ServerStatus;
  
  // Server specifications
  plan: string;
  region: string;
  os: string;
  vcpu_count: number;
  ram: number; // in MB
  disk: number; // in GB
  bandwidth: number; // in TB
  
  // Network configuration
  main_ip: string;
  internal_ip?: string;
  netmask_v4?: string;
  gateway_v4?: string;
  
  // Server configuration
  default_password?: string;
  date_created: Date;
  allowed_bandwidth: number;
  
  // Achidas specific
  users_count: number;
  max_users: number;
  cpu_usage?: number;
  memory_usage?: number;
  disk_usage?: number;
  
  // Metadata
  tags: string[];
  features: string[];
  created_at: Date;
  updated_at: Date;
  last_sync_at?: Date;
  
  // Cost tracking
  monthly_cost?: number;
  hourly_cost?: number;
}

// Server response interface
export interface ServerResponse {
  id: string;
  vultr_id: string;
  name: string;
  label: string;
  hostname?: string;
  type: ServerType;
  status: ServerStatus;
  plan: string;
  region: string;
  os: string;
  vcpu_count: number;
  ram: number;
  disk: number;
  bandwidth: number;
  main_ip: string;
  internal_ip?: string;
  date_created: string;
  allowed_bandwidth: number;
  users_count: number;
  max_users: number;
  cpu_usage?: number;
  memory_usage?: number;
  disk_usage?: number;
  tags: string[];
  features: string[];
  created_at: string;
  updated_at: string;
  last_sync_at?: string;
  monthly_cost?: number;
  hourly_cost?: number;
}

// Validation schemas
export const createServerSchema = z.object({
  vultr_id: z.string().min(1, 'Vultr ID is required'),
  name: z.string().min(1, 'Server name is required'),
  label: z.string().min(1, 'Server label is required'),
  hostname: z.string().optional(),
  type: z.nativeEnum(ServerType),
  plan: z.string().min(1, 'Plan is required'),
  region: z.string().min(1, 'Region is required'),
  os: z.string().min(1, 'OS is required'),
  vcpu_count: z.number().min(1),
  ram: z.number().min(1),
  disk: z.number().min(1),
  bandwidth: z.number().min(0),
  main_ip: z.string().ip('Invalid IP address'),
  internal_ip: z.string().ip('Invalid IP address').optional(),
  netmask_v4: z.string().optional(),
  gateway_v4: z.string().ip('Invalid gateway IP').optional(),
  default_password: z.string().optional(),
  allowed_bandwidth: z.number().min(0),
  max_users: z.number().min(1).default(50),
  tags: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  monthly_cost: z.number().min(0).optional(),
  hourly_cost: z.number().min(0).optional(),
});

export const updateServerSchema = createServerSchema.partial().omit({ vultr_id: true });

export const serverMetricsSchema = z.object({
  cpu_usage: z.number().min(0).max(100).optional(),
  memory_usage: z.number().min(0).max(100).optional(),
  disk_usage: z.number().min(0).max(100).optional(),
  users_count: z.number().min(0).optional(),
});

// Type exports for request/response
export type CreateServerRequest = z.infer<typeof createServerSchema>;
export type UpdateServerRequest = z.infer<typeof updateServerSchema>;
export type ServerMetricsRequest = z.infer<typeof serverMetricsSchema>;

// Helper function to convert Server to ServerResponse
export function toServerResponse(server: Server): ServerResponse {
  return {
    id: server._id?.toString() || server.id || '',
    vultr_id: server.vultr_id,
    name: server.name,
    label: server.label,
    hostname: server.hostname,
    type: server.type,
    status: server.status,
    plan: server.plan,
    region: server.region,
    os: server.os,
    vcpu_count: server.vcpu_count,
    ram: server.ram,
    disk: server.disk,
    bandwidth: server.bandwidth,
    main_ip: server.main_ip,
    internal_ip: server.internal_ip,
    date_created: server.date_created.toISOString(),
    allowed_bandwidth: server.allowed_bandwidth,
    users_count: server.users_count,
    max_users: server.max_users,
    cpu_usage: server.cpu_usage,
    memory_usage: server.memory_usage,
    disk_usage: server.disk_usage,
    tags: server.tags,
    features: server.features,
    created_at: server.created_at.toISOString(),
    updated_at: server.updated_at.toISOString(),
    last_sync_at: server.last_sync_at?.toISOString(),
    monthly_cost: server.monthly_cost,
    hourly_cost: server.hourly_cost,
  };
}
