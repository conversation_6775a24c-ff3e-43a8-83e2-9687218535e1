"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getServersController = getServersController;
exports.getServerController = getServerController;
exports.createServerController = createServerController;
exports.deleteServerController = deleteServerController;
exports.startServerController = startServerController;
exports.stopServerController = stopServerController;
exports.rebootServerController = rebootServerController;
exports.getPlansController = getPlansController;
exports.getRegionsController = getRegionsController;
exports.getBillingController = getBillingController;
exports.getAccountInfoController = getAccountInfoController;
exports.getOperatingSystemsController = getOperatingSystemsController;
exports.getBlockStorageController = getBlockStorageController;
exports.createBlockStorageController = createBlockStorageController;
exports.deleteBlockStorageController = deleteBlockStorageController;
exports.attachBlockStorageController = attachBlockStorageController;
exports.detachBlockStorageController = detachBlockStorageController;
exports.getSSHKeysController = getSSHKeysController;
exports.getSnapshotsController = getSnapshotsController;
exports.getLoadBalancersController = getLoadBalancersController;
const vultr_1 = require("../services/vultr");
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
async function getServersController(_request, reply) {
    try {
        const servers = await vultr_1.vultrService.getServers();
        logger_1.logger.info(`Retrieved ${servers.length} servers from Vultr`);
        return response_1.ResponseHelper.success(reply, servers);
    }
    catch (error) {
        logger_1.logger.error('Get servers controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch servers');
    }
}
async function getServerController(request, reply) {
    try {
        const { id } = request.params;
        const server = await vultr_1.vultrService.getServer(id);
        logger_1.logger.info(`Retrieved server ${id} from Vultr`);
        return response_1.ResponseHelper.success(reply, server);
    }
    catch (error) {
        logger_1.logger.error('Get server controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Server not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server');
    }
}
async function createServerController(request, reply) {
    try {
        const serverData = request.body;
        if (!serverData.region || !serverData.plan || !serverData.os) {
            return response_1.ResponseHelper.validationError(reply, 'Missing required fields: region, plan, os');
        }
        const server = await vultr_1.vultrService.createServer(serverData);
        logger_1.logger.info(`Created server ${server.id} on Vultr`);
        return response_1.ResponseHelper.success(reply, server, 201);
    }
    catch (error) {
        logger_1.logger.error('Create server controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to create server');
    }
}
async function deleteServerController(request, reply) {
    try {
        const { id } = request.params;
        await vultr_1.vultrService.deleteServer(id);
        logger_1.logger.info(`Deleted server ${id} from Vultr`);
        return response_1.ResponseHelper.success(reply, { message: 'Server deleted successfully' });
    }
    catch (error) {
        logger_1.logger.error('Delete server controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Server not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to delete server');
    }
}
async function startServerController(request, reply) {
    try {
        const { id } = request.params;
        await vultr_1.vultrService.startServer(id);
        logger_1.logger.info(`Started server ${id} on Vultr`);
        return response_1.ResponseHelper.success(reply, { message: 'Server start initiated' });
    }
    catch (error) {
        logger_1.logger.error('Start server controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Server not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to start server');
    }
}
async function stopServerController(request, reply) {
    try {
        const { id } = request.params;
        await vultr_1.vultrService.stopServer(id);
        logger_1.logger.info(`Stopped server ${id} on Vultr`);
        return response_1.ResponseHelper.success(reply, { message: 'Server stop initiated' });
    }
    catch (error) {
        logger_1.logger.error('Stop server controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Server not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to stop server');
    }
}
async function rebootServerController(request, reply) {
    try {
        const { id } = request.params;
        await vultr_1.vultrService.rebootServer(id);
        logger_1.logger.info(`Rebooted server ${id} on Vultr`);
        return response_1.ResponseHelper.success(reply, { message: 'Server reboot initiated' });
    }
    catch (error) {
        logger_1.logger.error('Reboot server controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Server not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to reboot server');
    }
}
async function getPlansController(_request, reply) {
    try {
        const plans = await vultr_1.vultrService.getPlans();
        logger_1.logger.info(`Retrieved ${plans.length} plans from Vultr`);
        return response_1.ResponseHelper.success(reply, plans);
    }
    catch (error) {
        logger_1.logger.error('Get plans controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch plans');
    }
}
async function getRegionsController(_request, reply) {
    try {
        const regions = await vultr_1.vultrService.getRegions();
        logger_1.logger.info(`Retrieved ${regions.length} regions from Vultr`);
        return response_1.ResponseHelper.success(reply, regions);
    }
    catch (error) {
        logger_1.logger.error('Get regions controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch regions');
    }
}
async function getBillingController(_request, reply) {
    try {
        const billing = await vultr_1.vultrService.getBilling();
        logger_1.logger.info('Retrieved billing information from Vultr');
        return response_1.ResponseHelper.success(reply, billing);
    }
    catch (error) {
        logger_1.logger.error('Get billing controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch billing information');
    }
}
async function getAccountInfoController(_request, reply) {
    try {
        const account = await vultr_1.vultrService.getAccountInfo();
        logger_1.logger.info('Retrieved account information from Vultr');
        return response_1.ResponseHelper.success(reply, account);
    }
    catch (error) {
        logger_1.logger.error('Get account info controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch account information');
    }
}
async function getOperatingSystemsController(_request, reply) {
    try {
        const operatingSystems = await vultr_1.vultrService.getOperatingSystems();
        logger_1.logger.info(`Retrieved ${operatingSystems.length} operating systems from Vultr`);
        return response_1.ResponseHelper.success(reply, operatingSystems);
    }
    catch (error) {
        logger_1.logger.error('Get operating systems controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch operating systems');
    }
}
async function getBlockStorageController(_request, reply) {
    try {
        const blockStorage = await vultr_1.vultrService.getBlockStorage();
        logger_1.logger.info(`Retrieved ${blockStorage.length} block storage volumes from Vultr`);
        return response_1.ResponseHelper.success(reply, blockStorage);
    }
    catch (error) {
        logger_1.logger.error('Get block storage controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch block storage');
    }
}
async function createBlockStorageController(request, reply) {
    try {
        const storageData = request.body;
        if (!storageData.region || !storageData.size_gb) {
            return response_1.ResponseHelper.validationError(reply, 'Missing required fields: region, size_gb');
        }
        const blockStorage = await vultr_1.vultrService.createBlockStorage(storageData);
        logger_1.logger.info(`Created block storage ${blockStorage.id} on Vultr`);
        return response_1.ResponseHelper.success(reply, blockStorage, 201);
    }
    catch (error) {
        logger_1.logger.error('Create block storage controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to create block storage');
    }
}
async function deleteBlockStorageController(request, reply) {
    try {
        const { id } = request.params;
        await vultr_1.vultrService.deleteBlockStorage(id);
        logger_1.logger.info(`Deleted block storage ${id} from Vultr`);
        return response_1.ResponseHelper.success(reply, { message: 'Block storage deleted successfully' });
    }
    catch (error) {
        logger_1.logger.error('Delete block storage controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Block storage not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to delete block storage');
    }
}
async function attachBlockStorageController(request, reply) {
    try {
        const { id } = request.params;
        const { instance_id } = request.body;
        if (!instance_id) {
            return response_1.ResponseHelper.validationError(reply, 'instance_id is required');
        }
        await vultr_1.vultrService.attachBlockStorage(id, instance_id);
        logger_1.logger.info(`Attached block storage ${id} to instance ${instance_id} on Vultr`);
        return response_1.ResponseHelper.success(reply, { message: 'Block storage attached successfully' });
    }
    catch (error) {
        logger_1.logger.error('Attach block storage controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to attach block storage');
    }
}
async function detachBlockStorageController(request, reply) {
    try {
        const { id } = request.params;
        await vultr_1.vultrService.detachBlockStorage(id);
        logger_1.logger.info(`Detached block storage ${id} from Vultr`);
        return response_1.ResponseHelper.success(reply, { message: 'Block storage detached successfully' });
    }
    catch (error) {
        logger_1.logger.error('Detach block storage controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to detach block storage');
    }
}
async function getSSHKeysController(_request, reply) {
    try {
        const sshKeys = await vultr_1.vultrService.getSSHKeys();
        logger_1.logger.info(`Retrieved ${sshKeys.length} SSH keys from Vultr`);
        return response_1.ResponseHelper.success(reply, sshKeys);
    }
    catch (error) {
        logger_1.logger.error('Get SSH keys controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch SSH keys');
    }
}
async function getSnapshotsController(_request, reply) {
    try {
        const snapshots = await vultr_1.vultrService.getSnapshots();
        logger_1.logger.info(`Retrieved ${snapshots.length} snapshots from Vultr`);
        return response_1.ResponseHelper.success(reply, snapshots);
    }
    catch (error) {
        logger_1.logger.error('Get snapshots controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch snapshots');
    }
}
async function getLoadBalancersController(_request, reply) {
    try {
        const loadBalancers = await vultr_1.vultrService.getLoadBalancers();
        logger_1.logger.info(`Retrieved ${loadBalancers.length} load balancers from Vultr`);
        return response_1.ResponseHelper.success(reply, loadBalancers);
    }
    catch (error) {
        logger_1.logger.error('Get load balancers controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch load balancers');
    }
}
//# sourceMappingURL=vultr.controller.js.map