# Achidas Backend API

A comprehensive TypeScript Fastify backend for the Achidas cloud platform, providing APIs for user management, application deployment, and Vultr infrastructure integration.

## Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Application Management**: Full CRUD operations for applications with deployment support
- **Vultr Integration**: Complete Vultr API integration for server and resource management
- **Validation**: Comprehensive request validation using Zod schemas
- **Error Handling**: Structured error handling with custom error classes
- **Rate Limiting**: Built-in rate limiting for API protection
- **Logging**: Structured logging with Pino
- **Database**: MongoDB integration with connection pooling
- **TypeScript**: Full TypeScript support with strict type checking

## Tech Stack

- **Framework**: Fastify 4.x
- **Language**: TypeScript 5.x
- **Database**: MongoDB 6.x
- **Validation**: Zod
- **Authentication**: JWT + bcryptjs
- **Logging**: Pino
- **HTTP Client**: Axios
- **Testing**: Jest

## Project Structure

```
backend/
├── src/
│   ├── config/           # Configuration management
│   ├── controllers/      # Route controllers with separate functions
│   ├── database/         # Database connection and setup
│   ├── middleware/       # Custom middleware (auth, validation, etc.)
│   ├── models/           # TypeScript interfaces and Zod schemas
│   ├── routes/           # Route definitions
│   ├── services/         # Business logic services
│   ├── utils/            # Utility functions and helpers
│   ├── app.ts           # Fastify app setup
│   └── server.ts        # Server entry point
├── dist/                # Compiled JavaScript output
├── package.json
├── tsconfig.json
└── README.md
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- MongoDB 6+
- Vultr API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd achidas/backend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Server Configuration
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# Database
DATABASE_URL=mongodb://localhost:27017/achidas

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Vultr API
VULTR_API_KEY=your-vultr-api-key
VULTR_BASE_URL=https://api.vultr.com/v2

# CORS
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_TIME_WINDOW=60000

# Logging
LOG_LEVEL=info
```

4. Start the development server:
```bash
npm run dev
```

The server will start on `http://localhost:3000`

### Building for Production

```bash
npm run build
npm start
```

## API Documentation

### Authentication Endpoints

#### POST `/api/v1/auth/register`
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe",
  "company": "Company Name",
  "phone": "+**********"
}
```

#### POST `/api/v1/auth/login`
Authenticate user and get JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "jwt-token-here",
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "username": "username",
      "role": "user"
    },
    "expires_at": "2024-01-01T00:00:00.000Z"
  }
}
```

#### GET `/api/v1/auth/me`
Get current user profile (requires authentication).

### Application Management Endpoints

#### GET `/api/v1/applications`
Get user's applications with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `sort` (optional): Sort field (default: created_at)
- `order` (optional): Sort order - asc/desc (default: desc)

#### POST `/api/v1/applications`
Create a new application.

**Request Body:**
```json
{
  "name": "My App",
  "description": "Application description",
  "type": "web_service",
  "repository": {
    "url": "https://github.com/user/repo",
    "branch": "main",
    "auto_deploy": true
  },
  "environment": {
    "NODE_ENV": "production"
  },
  "deployment": {
    "build_command": "npm run build",
    "start_command": "npm start"
  },
  "resources": {
    "cpu": 1,
    "memory": 512,
    "disk": 10
  }
}
```

#### GET `/api/v1/applications/:id`
Get application by ID.

#### PUT `/api/v1/applications/:id`
Update application.

#### DELETE `/api/v1/applications/:id`
Delete application.

#### POST `/api/v1/applications/:id/deploy`
Deploy application.

#### POST `/api/v1/applications/:id/stop`
Stop application.

### Compute (Vultr) Endpoints

#### GET `/api/v1/compute/servers`
Get all servers from Vultr.

#### GET `/api/v1/compute/servers/:id`
Get server by ID.

#### POST `/api/v1/compute/servers` (Admin only)
Create a new server.

#### DELETE `/api/v1/compute/servers/:id` (Admin only)
Delete server.

#### GET `/api/v1/compute/plans`
Get available Vultr plans.

#### GET `/api/v1/compute/regions`
Get available Vultr regions.

#### GET `/api/v1/compute/os`
Get available operating systems.

### Health Check

#### GET `/health`
Get server health status.

## Error Handling

All API responses follow a consistent format:

**Success Response:**
```json
{
  "success": true,
  "data": { ... },
  "metadata": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "request_id": "req-123",
    "version": "1.0.0"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": {
      "errors": [
        {
          "field": "email",
          "message": "Invalid email format"
        }
      ]
    }
  },
  "metadata": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "request_id": "req-123"
  }
}
```

## Authentication

Most endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Rate Limiting

API requests are rate limited to 100 requests per minute per IP address. Rate limit headers are included in responses:

- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time

## Development

### Running Tests

```bash
npm test
npm run test:watch
npm run test:coverage
```

### Linting

```bash
npm run lint
npm run lint:fix
```

### Environment Variables

See `.env.example` for all available configuration options.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## License

MIT License - see LICENSE file for details.
