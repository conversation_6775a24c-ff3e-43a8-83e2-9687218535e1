import { FastifyRequest, FastifyReply } from 'fastify';
import { vultrService } from '../services/vultr';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';

// Get all servers controller
export async function getServersController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const servers = await vultrService.getServers();
    
    logger.info(`Retrieved ${servers.length} servers from Vultr`);
    
    return ResponseHelper.success(reply, servers);
  } catch (error) {
    logger.error('Get servers controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch servers');
  }
}

// Get server by ID controller
export async function getServerController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;
    
    const server = await vultrService.getServer(id);
    
    logger.info(`Retrieved server ${id} from Vultr`);
    
    return ResponseHelper.success(reply, server);
  } catch (error) {
    logger.error('Get server controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Server not found');
    }
    
    return ResponseHelper.internalError(reply, 'Failed to fetch server');
  }
}

// Create server controller
export async function createServerController(
  request: FastifyRequest<{ Body: any }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const serverData = request.body;
    
    // Basic validation
    if (!serverData.region || !serverData.plan || !serverData.os) {
      return ResponseHelper.validationError(
        reply,
        'Missing required fields: region, plan, os'
      );
    }
    
    const server = await vultrService.createServer(serverData);
    
    logger.info(`Created server ${server.id} on Vultr`);
    
    return ResponseHelper.success(reply, server, 201);
  } catch (error) {
    logger.error('Create server controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to create server');
  }
}

// Delete server controller
export async function deleteServerController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;
    
    await vultrService.deleteServer(id);
    
    logger.info(`Deleted server ${id} from Vultr`);
    
    return ResponseHelper.success(reply, { message: 'Server deleted successfully' });
  } catch (error) {
    logger.error('Delete server controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Server not found');
    }
    
    return ResponseHelper.internalError(reply, 'Failed to delete server');
  }
}

// Start server controller
export async function startServerController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;
    
    await vultrService.startServer(id);
    
    logger.info(`Started server ${id} on Vultr`);
    
    return ResponseHelper.success(reply, { message: 'Server start initiated' });
  } catch (error) {
    logger.error('Start server controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Server not found');
    }
    
    return ResponseHelper.internalError(reply, 'Failed to start server');
  }
}

// Stop server controller
export async function stopServerController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;
    
    await vultrService.stopServer(id);
    
    logger.info(`Stopped server ${id} on Vultr`);
    
    return ResponseHelper.success(reply, { message: 'Server stop initiated' });
  } catch (error) {
    logger.error('Stop server controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Server not found');
    }
    
    return ResponseHelper.internalError(reply, 'Failed to stop server');
  }
}

// Reboot server controller
export async function rebootServerController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;
    
    await vultrService.rebootServer(id);
    
    logger.info(`Rebooted server ${id} on Vultr`);
    
    return ResponseHelper.success(reply, { message: 'Server reboot initiated' });
  } catch (error) {
    logger.error('Reboot server controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Server not found');
    }
    
    return ResponseHelper.internalError(reply, 'Failed to reboot server');
  }
}

// Get plans controller
export async function getPlansController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const plans = await vultrService.getPlans();
    
    logger.info(`Retrieved ${plans.length} plans from Vultr`);
    
    return ResponseHelper.success(reply, plans);
  } catch (error) {
    logger.error('Get plans controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch plans');
  }
}

// Get regions controller
export async function getRegionsController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const regions = await vultrService.getRegions();
    
    logger.info(`Retrieved ${regions.length} regions from Vultr`);
    
    return ResponseHelper.success(reply, regions);
  } catch (error) {
    logger.error('Get regions controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch regions');
  }
}

// Get billing controller
export async function getBillingController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const billing = await vultrService.getBilling();
    
    logger.info('Retrieved billing information from Vultr');
    
    return ResponseHelper.success(reply, billing);
  } catch (error) {
    logger.error('Get billing controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch billing information');
  }
}

// Get account info controller
export async function getAccountInfoController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const account = await vultrService.getAccountInfo();
    
    logger.info('Retrieved account information from Vultr');
    
    return ResponseHelper.success(reply, account);
  } catch (error) {
    logger.error('Get account info controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch account information');
  }
}

// Get operating systems controller
export async function getOperatingSystemsController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const operatingSystems = await vultrService.getOperatingSystems();

    logger.info(`Retrieved ${operatingSystems.length} operating systems from Vultr`);

    return ResponseHelper.success(reply, operatingSystems);
  } catch (error) {
    logger.error('Get operating systems controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch operating systems');
  }
}

// Get block storage controller
export async function getBlockStorageController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const blockStorage = await vultrService.getBlockStorage();

    logger.info(`Retrieved ${blockStorage.length} block storage volumes from Vultr`);

    return ResponseHelper.success(reply, blockStorage);
  } catch (error) {
    logger.error('Get block storage controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch block storage');
  }
}

// Create block storage controller
export async function createBlockStorageController(
  request: FastifyRequest<{ Body: any }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const storageData = request.body;

    // Basic validation
    if (!storageData.region || !storageData.size_gb) {
      return ResponseHelper.validationError(
        reply,
        'Missing required fields: region, size_gb'
      );
    }

    const blockStorage = await vultrService.createBlockStorage(storageData);

    logger.info(`Created block storage ${blockStorage.id} on Vultr`);

    return ResponseHelper.success(reply, blockStorage, 201);
  } catch (error) {
    logger.error('Create block storage controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to create block storage');
  }
}

// Delete block storage controller
export async function deleteBlockStorageController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;

    await vultrService.deleteBlockStorage(id);

    logger.info(`Deleted block storage ${id} from Vultr`);

    return ResponseHelper.success(reply, { message: 'Block storage deleted successfully' });
  } catch (error) {
    logger.error('Delete block storage controller error:', error);

    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Block storage not found');
    }

    return ResponseHelper.internalError(reply, 'Failed to delete block storage');
  }
}

// Attach block storage controller
export async function attachBlockStorageController(
  request: FastifyRequest<{
    Params: { id: string };
    Body: { instance_id: string }
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;
    const { instance_id } = request.body;

    if (!instance_id) {
      return ResponseHelper.validationError(reply, 'instance_id is required');
    }

    await vultrService.attachBlockStorage(id, instance_id);

    logger.info(`Attached block storage ${id} to instance ${instance_id} on Vultr`);

    return ResponseHelper.success(reply, { message: 'Block storage attached successfully' });
  } catch (error) {
    logger.error('Attach block storage controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to attach block storage');
  }
}

// Detach block storage controller
export async function detachBlockStorageController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { id } = request.params;

    await vultrService.detachBlockStorage(id);

    logger.info(`Detached block storage ${id} from Vultr`);

    return ResponseHelper.success(reply, { message: 'Block storage detached successfully' });
  } catch (error) {
    logger.error('Detach block storage controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to detach block storage');
  }
}

// Get SSH keys controller
export async function getSSHKeysController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const sshKeys = await vultrService.getSSHKeys();

    logger.info(`Retrieved ${sshKeys.length} SSH keys from Vultr`);

    return ResponseHelper.success(reply, sshKeys);
  } catch (error) {
    logger.error('Get SSH keys controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch SSH keys');
  }
}

// Get snapshots controller
export async function getSnapshotsController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const snapshots = await vultrService.getSnapshots();

    logger.info(`Retrieved ${snapshots.length} snapshots from Vultr`);

    return ResponseHelper.success(reply, snapshots);
  } catch (error) {
    logger.error('Get snapshots controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch snapshots');
  }
}

// Get load balancers controller
export async function getLoadBalancersController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const loadBalancers = await vultrService.getLoadBalancers();

    logger.info(`Retrieved ${loadBalancers.length} load balancers from Vultr`);

    return ResponseHelper.success(reply, loadBalancers);
  } catch (error) {
    logger.error('Get load balancers controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch load balancers');
  }
}
