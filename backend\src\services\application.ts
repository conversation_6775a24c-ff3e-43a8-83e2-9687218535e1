import { Collection, ObjectId } from 'mongodb';
import { databaseConnection } from '../database/connection';
import { 
  Application, 
  ApplicationStatus,
  CreateApplicationRequest, 
  UpdateApplicationRequest,
  toApplicationResponse,
  ApplicationResponse,
  PaginationOptions,
  PaginatedResponse
} from '../models';
import { logger } from '../utils/logger';

export class ApplicationService {
  private applicationsCollection: Collection<Application>;

  constructor() {
    this.applicationsCollection = databaseConnection.getCollection<Application>('applications');
  }

  async createApplication(userId: string, applicationData: CreateApplicationRequest): Promise<ApplicationResponse> {
    try {
      // Check if application name already exists for this user
      const existingApp = await this.applicationsCollection.findOne({
        user_id: userId,
        name: applicationData.name
      });

      if (existingApp) {
        throw new Error('Application with this name already exists');
      }

      // Create application object
      const now = new Date();
      const newApplication: Application = {
        user_id: userId,
        name: applicationData.name,
        description: applicationData.description,
        type: applicationData.type,
        status: ApplicationStatus.DRAFT,
        repository: applicationData.repository,
        environment: applicationData.environment,
        deployment: applicationData.deployment,
        resources: applicationData.resources || {},
        created_at: now,
        updated_at: now,
        stats: {
          total_deployments: 0,
          successful_deployments: 0,
          failed_deployments: 0,
          last_deployment_status: 'none',
        },
      } as Application;

      // Insert application into database
      const result = await this.applicationsCollection.insertOne(newApplication);
      
      // Fetch the created application
      const createdApplication = await this.applicationsCollection.findOne({ _id: result.insertedId });
      
      if (!createdApplication) {
        throw new Error('Failed to create application');
      }

      logger.info(`Application created: ${applicationData.name} for user: ${userId}`);
      return toApplicationResponse(createdApplication);
    } catch (error) {
      logger.error('Create application error:', error);
      throw error;
    }
  }

  async getApplicationById(applicationId: string, userId?: string): Promise<ApplicationResponse> {
    try {
      const query: any = { _id: new ObjectId(applicationId) };
      
      // If userId is provided, ensure user owns the application
      if (userId) {
        query.user_id = userId;
      }

      const application = await this.applicationsCollection.findOne(query);
      
      if (!application) {
        throw new Error('Application not found');
      }

      return toApplicationResponse(application);
    } catch (error) {
      logger.error('Get application by ID error:', error);
      throw error;
    }
  }

  async getApplicationsByUser(
    userId: string, 
    options: PaginationOptions = {}
  ): Promise<PaginatedResponse<ApplicationResponse>> {
    try {
      const { page = 1, limit = 10, sort = 'created_at', order = 'desc' } = options;
      const skip = (page - 1) * limit;

      // Build sort object
      const sortObj: any = {};
      sortObj[sort] = order === 'asc' ? 1 : -1;

      // Get applications with pagination
      const applications = await this.applicationsCollection
        .find({ user_id: userId })
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .toArray();

      // Get total count
      const total = await this.applicationsCollection.countDocuments({ user_id: userId });

      const pages = Math.ceil(total / limit);

      return {
        data: applications.map(toApplicationResponse),
        pagination: {
          page,
          limit,
          total,
          pages,
          has_next: page < pages,
          has_prev: page > 1,
        },
      };
    } catch (error) {
      logger.error('Get applications by user error:', error);
      throw error;
    }
  }

  async updateApplication(
    applicationId: string, 
    userId: string, 
    updateData: UpdateApplicationRequest
  ): Promise<ApplicationResponse> {
    try {
      // Check if application exists and user owns it
      const existingApp = await this.applicationsCollection.findOne({
        _id: new ObjectId(applicationId),
        user_id: userId
      });

      if (!existingApp) {
        throw new Error('Application not found');
      }

      // If name is being updated, check for conflicts
      if (updateData.name && updateData.name !== existingApp.name) {
        const nameConflict = await this.applicationsCollection.findOne({
          user_id: userId,
          name: updateData.name,
          _id: { $ne: new ObjectId(applicationId) }
        });

        if (nameConflict) {
          throw new Error('Application with this name already exists');
        }
      }

      // Update application
      const updateFields: any = {
        ...updateData,
        updated_at: new Date(),
      };

      const result = await this.applicationsCollection.updateOne(
        { _id: new ObjectId(applicationId), user_id: userId },
        { $set: updateFields }
      );

      if (result.matchedCount === 0) {
        throw new Error('Application not found');
      }

      // Fetch updated application
      const updatedApplication = await this.applicationsCollection.findOne({
        _id: new ObjectId(applicationId)
      });

      if (!updatedApplication) {
        throw new Error('Failed to fetch updated application');
      }

      logger.info(`Application updated: ${applicationId} for user: ${userId}`);
      return toApplicationResponse(updatedApplication);
    } catch (error) {
      logger.error('Update application error:', error);
      throw error;
    }
  }

  async deleteApplication(applicationId: string, userId: string): Promise<void> {
    try {
      const result = await this.applicationsCollection.deleteOne({
        _id: new ObjectId(applicationId),
        user_id: userId
      });

      if (result.deletedCount === 0) {
        throw new Error('Application not found');
      }

      logger.info(`Application deleted: ${applicationId} for user: ${userId}`);
    } catch (error) {
      logger.error('Delete application error:', error);
      throw error;
    }
  }

  async updateApplicationStatus(
    applicationId: string, 
    status: ApplicationStatus,
    userId?: string
  ): Promise<ApplicationResponse> {
    try {
      const query: any = { _id: new ObjectId(applicationId) };
      
      if (userId) {
        query.user_id = userId;
      }

      const result = await this.applicationsCollection.updateOne(
        query,
        { 
          $set: { 
            status,
            updated_at: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        throw new Error('Application not found');
      }

      // Fetch updated application
      const updatedApplication = await this.applicationsCollection.findOne({
        _id: new ObjectId(applicationId)
      });

      if (!updatedApplication) {
        throw new Error('Failed to fetch updated application');
      }

      logger.info(`Application status updated: ${applicationId} to ${status}`);
      return toApplicationResponse(updatedApplication);
    } catch (error) {
      logger.error('Update application status error:', error);
      throw error;
    }
  }

  async getApplicationStats(userId: string): Promise<any> {
    try {
      const pipeline = [
        { $match: { user_id: userId } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ];

      const statusCounts = await this.applicationsCollection.aggregate(pipeline).toArray();
      
      const stats = {
        total: 0,
        draft: 0,
        building: 0,
        deployed: 0,
        failed: 0,
        stopped: 0,
        suspended: 0,
      };

      statusCounts.forEach(item => {
        stats.total += item['count'];
        stats[item['_id'] as keyof typeof stats] = item['count'];
      });

      return stats;
    } catch (error) {
      logger.error('Get application stats error:', error);
      throw error;
    }
  }
}

// Lazy singleton instance
let applicationServiceInstance: ApplicationService | null = null;

export function getApplicationService(): ApplicationService {
  if (!applicationServiceInstance) {
    applicationServiceInstance = new ApplicationService();
  }
  return applicationServiceInstance;
}
