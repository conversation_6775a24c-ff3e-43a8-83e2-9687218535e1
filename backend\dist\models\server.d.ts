import { ObjectId } from 'mongodb';
import { z } from 'zod';
export declare enum ServerStatus {
    ACTIVE = "active",
    PENDING = "pending",
    SUSPENDED = "suspended",
    INSTALLING = "installing",
    STOPPED = "stopped",
    MAINTENANCE = "maintenance"
}
export declare enum ServerType {
    SHARED = "shared",
    DEDICATED = "dedicated",
    ENTERPRISE = "enterprise"
}
export interface Server {
    _id?: ObjectId;
    id?: string;
    vultr_id: string;
    name: string;
    label: string;
    hostname?: string;
    type: ServerType;
    status: ServerStatus;
    plan: string;
    region: string;
    os: string;
    vcpu_count: number;
    ram: number;
    disk: number;
    bandwidth: number;
    main_ip: string;
    internal_ip?: string;
    netmask_v4?: string;
    gateway_v4?: string;
    default_password?: string;
    date_created: Date;
    allowed_bandwidth: number;
    users_count: number;
    max_users: number;
    cpu_usage?: number;
    memory_usage?: number;
    disk_usage?: number;
    tags: string[];
    features: string[];
    created_at: Date;
    updated_at: Date;
    last_sync_at?: Date;
    monthly_cost?: number;
    hourly_cost?: number;
}
export interface ServerResponse {
    id: string;
    vultr_id: string;
    name: string;
    label: string;
    hostname?: string;
    type: ServerType;
    status: ServerStatus;
    plan: string;
    region: string;
    os: string;
    vcpu_count: number;
    ram: number;
    disk: number;
    bandwidth: number;
    main_ip: string;
    internal_ip?: string;
    date_created: string;
    allowed_bandwidth: number;
    users_count: number;
    max_users: number;
    cpu_usage?: number;
    memory_usage?: number;
    disk_usage?: number;
    tags: string[];
    features: string[];
    created_at: string;
    updated_at: string;
    last_sync_at?: string;
    monthly_cost?: number;
    hourly_cost?: number;
}
export declare const createServerSchema: z.ZodObject<{
    vultr_id: z.ZodString;
    name: z.ZodString;
    label: z.ZodString;
    hostname: z.ZodOptional<z.ZodString>;
    type: z.ZodNativeEnum<typeof ServerType>;
    plan: z.ZodString;
    region: z.ZodString;
    os: z.ZodString;
    vcpu_count: z.ZodNumber;
    ram: z.ZodNumber;
    disk: z.ZodNumber;
    bandwidth: z.ZodNumber;
    main_ip: z.ZodString;
    internal_ip: z.ZodOptional<z.ZodString>;
    netmask_v4: z.ZodOptional<z.ZodString>;
    gateway_v4: z.ZodOptional<z.ZodString>;
    default_password: z.ZodOptional<z.ZodString>;
    allowed_bandwidth: z.ZodNumber;
    max_users: z.ZodDefault<z.ZodNumber>;
    tags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    features: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    monthly_cost: z.ZodOptional<z.ZodNumber>;
    hourly_cost: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    type: ServerType;
    name: string;
    vultr_id: string;
    region: string;
    plan: string;
    label: string;
    os: string;
    vcpu_count: number;
    ram: number;
    disk: number;
    bandwidth: number;
    main_ip: string;
    allowed_bandwidth: number;
    max_users: number;
    tags: string[];
    features: string[];
    hostname?: string | undefined;
    internal_ip?: string | undefined;
    netmask_v4?: string | undefined;
    gateway_v4?: string | undefined;
    default_password?: string | undefined;
    monthly_cost?: number | undefined;
    hourly_cost?: number | undefined;
}, {
    type: ServerType;
    name: string;
    vultr_id: string;
    region: string;
    plan: string;
    label: string;
    os: string;
    vcpu_count: number;
    ram: number;
    disk: number;
    bandwidth: number;
    main_ip: string;
    allowed_bandwidth: number;
    hostname?: string | undefined;
    internal_ip?: string | undefined;
    netmask_v4?: string | undefined;
    gateway_v4?: string | undefined;
    default_password?: string | undefined;
    max_users?: number | undefined;
    tags?: string[] | undefined;
    features?: string[] | undefined;
    monthly_cost?: number | undefined;
    hourly_cost?: number | undefined;
}>;
export declare const updateServerSchema: z.ZodObject<Omit<{
    vultr_id: z.ZodOptional<z.ZodString>;
    name: z.ZodOptional<z.ZodString>;
    label: z.ZodOptional<z.ZodString>;
    hostname: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    type: z.ZodOptional<z.ZodNativeEnum<typeof ServerType>>;
    plan: z.ZodOptional<z.ZodString>;
    region: z.ZodOptional<z.ZodString>;
    os: z.ZodOptional<z.ZodString>;
    vcpu_count: z.ZodOptional<z.ZodNumber>;
    ram: z.ZodOptional<z.ZodNumber>;
    disk: z.ZodOptional<z.ZodNumber>;
    bandwidth: z.ZodOptional<z.ZodNumber>;
    main_ip: z.ZodOptional<z.ZodString>;
    internal_ip: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    netmask_v4: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    gateway_v4: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    default_password: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    allowed_bandwidth: z.ZodOptional<z.ZodNumber>;
    max_users: z.ZodOptional<z.ZodDefault<z.ZodNumber>>;
    tags: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
    features: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
    monthly_cost: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    hourly_cost: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
}, "vultr_id">, "strip", z.ZodTypeAny, {
    type?: ServerType | undefined;
    name?: string | undefined;
    region?: string | undefined;
    plan?: string | undefined;
    label?: string | undefined;
    hostname?: string | undefined;
    os?: string | undefined;
    vcpu_count?: number | undefined;
    ram?: number | undefined;
    disk?: number | undefined;
    bandwidth?: number | undefined;
    main_ip?: string | undefined;
    internal_ip?: string | undefined;
    netmask_v4?: string | undefined;
    gateway_v4?: string | undefined;
    default_password?: string | undefined;
    allowed_bandwidth?: number | undefined;
    max_users?: number | undefined;
    tags?: string[] | undefined;
    features?: string[] | undefined;
    monthly_cost?: number | undefined;
    hourly_cost?: number | undefined;
}, {
    type?: ServerType | undefined;
    name?: string | undefined;
    region?: string | undefined;
    plan?: string | undefined;
    label?: string | undefined;
    hostname?: string | undefined;
    os?: string | undefined;
    vcpu_count?: number | undefined;
    ram?: number | undefined;
    disk?: number | undefined;
    bandwidth?: number | undefined;
    main_ip?: string | undefined;
    internal_ip?: string | undefined;
    netmask_v4?: string | undefined;
    gateway_v4?: string | undefined;
    default_password?: string | undefined;
    allowed_bandwidth?: number | undefined;
    max_users?: number | undefined;
    tags?: string[] | undefined;
    features?: string[] | undefined;
    monthly_cost?: number | undefined;
    hourly_cost?: number | undefined;
}>;
export declare const serverMetricsSchema: z.ZodObject<{
    cpu_usage: z.ZodOptional<z.ZodNumber>;
    memory_usage: z.ZodOptional<z.ZodNumber>;
    disk_usage: z.ZodOptional<z.ZodNumber>;
    users_count: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    cpu_usage?: number | undefined;
    memory_usage?: number | undefined;
    disk_usage?: number | undefined;
    users_count?: number | undefined;
}, {
    cpu_usage?: number | undefined;
    memory_usage?: number | undefined;
    disk_usage?: number | undefined;
    users_count?: number | undefined;
}>;
export type CreateServerRequest = z.infer<typeof createServerSchema>;
export type UpdateServerRequest = z.infer<typeof updateServerSchema>;
export type ServerMetricsRequest = z.infer<typeof serverMetricsSchema>;
export declare function toServerResponse(server: Server): ServerResponse;
//# sourceMappingURL=server.d.ts.map