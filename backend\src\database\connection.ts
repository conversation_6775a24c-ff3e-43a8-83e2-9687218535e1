import { MongoClient, Db, Collection } from 'mongodb';
import { appConfig } from '../config';
import { logger } from '../utils/logger';

export class DatabaseConnection {
  private client: MongoClient | null = null;
  private database: Db | null = null;

  async connect(): Promise<void> {
    try {
      logger.info('Connecting to MongoDB...');
      
      this.client = new MongoClient(appConfig.database.url, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        family: 4, // Use IPv4, skip trying IPv6
      });

      await this.client.connect();
      
      // Extract database name from URL or use default
      const dbName = this.extractDatabaseName(appConfig.database.url) || 'achidas';
      this.database = this.client.db(dbName);
      
      // Test the connection
      await this.database.admin().ping();
      
      logger.info(`Successfully connected to MongoDB database: ${dbName}`);
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.database = null;
      logger.info('Disconnected from MongoDB');
    }
  }

  getDatabase(): Db {
    if (!this.database) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.database;
  }

  getCollection<T = any>(name: string): Collection<T> {
    return this.getDatabase().collection<T>(name);
  }

  private extractDatabaseName(url: string): string | null {
    try {
      const match = url.match(/\/([^/?]+)(\?|$)/);
      return match ? match[1] : null;
    } catch {
      return null;
    }
  }

  async createIndexes(): Promise<void> {
    try {
      const db = this.getDatabase();
      
      // Users collection indexes
      const usersCollection = db.collection('users');
      await usersCollection.createIndex({ email: 1 }, { unique: true });
      await usersCollection.createIndex({ username: 1 }, { unique: true });
      await usersCollection.createIndex({ status: 1 });
      await usersCollection.createIndex({ created_at: 1 });
      
      // Applications collection indexes
      const applicationsCollection = db.collection('applications');
      await applicationsCollection.createIndex({ user_id: 1 });
      await applicationsCollection.createIndex({ name: 1, user_id: 1 }, { unique: true });
      await applicationsCollection.createIndex({ status: 1 });
      await applicationsCollection.createIndex({ created_at: 1 });
      
      // Servers collection indexes
      const serversCollection = db.collection('servers');
      await serversCollection.createIndex({ vultr_id: 1 }, { unique: true });
      await serversCollection.createIndex({ status: 1 });
      await serversCollection.createIndex({ region: 1 });
      await serversCollection.createIndex({ plan: 1 });
      
      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Failed to create database indexes:', error);
      throw error;
    }
  }
}

// Singleton instance
export const databaseConnection = new DatabaseConnection();
