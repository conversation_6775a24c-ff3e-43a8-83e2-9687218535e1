{"version": 3, "file": "shared-hosting.js", "sourceRoot": "", "sources": ["../../src/services/shared-hosting.ts"], "names": [], "mappings": ";;;;;AAgiBA,0DAKC;AAriBD,4CAAyC;AACzC,iDAAqC;AACrC,+BAAiC;AACjC,2DAA6B;AAC7B,gDAAwB;AAExB,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAC;AAyElC,MAAM,oBAAoB;IACP,gBAAgB,GAAG,eAAe,CAAC;IACnC,SAAS,GAAG,IAAI,CAAC;IACjB,aAAa,GAAG,IAAI,CAAC;IACrB,aAAa,GAAG,IAAI,CAAC;IAGtC,KAAK,CAAC,UAAU,CAAC,QAAiC;QAChD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,QAAQ,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC;YAC1F,MAAM,aAAa,GAAG,YAAY,aAAa,EAAE,CAAC;YAClD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAGrD,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAGzD,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;YAG7D,MAAM,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAGtE,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAEzD,MAAM,UAAU,GAAsB;gBACpC,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;gBACrE,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,cAAc,EAAE,aAAa;gBAC7B,cAAc,EAAE,aAAa;gBAC7B,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,qBAAqB;gBAChC,SAAS,EAAE,IAAI,CAAC,gBAAgB;gBAChC,IAAI;gBACJ,QAAQ,EAAE,OAAO;gBACjB,QAAQ,EAAE,OAAO;gBACjB,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAClD,KAAK,EAAE;oBACL,SAAS,EAAE,CAAC;oBACZ,YAAY,EAAE,CAAC;oBACf,cAAc,EAAE,CAAC;oBACjB,YAAY,EAAE,CAAC;iBAChB;gBACD,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,YAAY,EAAE,EAAE;aACjB,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,gCAAgC,aAAa,OAAO,aAAa,EAAE,CAAC,CAAC;YACjF,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,aAAqB;QACnE,IAAI,CAAC;YAEH,MAAM,SAAS,CAAC,sDAAsD,aAAa,IAAI,QAAQ,EAAE,CAAC,CAAC;YAGnG,MAAM,SAAS,CAAC,kBAAkB,aAAa,EAAE,CAAC,CAAC;YACnD,MAAM,SAAS,CAAC,cAAc,QAAQ,IAAI,QAAQ,IAAI,aAAa,EAAE,CAAC,CAAC;YAGvE,MAAM,SAAS,CAAC,SAAS,QAAQ,gCAAgC,CAAC,CAAC;YAEnE,eAAM,CAAC,IAAI,CAAC,uBAAuB,QAAQ,eAAe,aAAa,EAAE,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,IAAY;QAC9D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAGxC,MAAM,WAAW,GAAG;;WAEf,MAAM,CAAC,SAAS;YACf,MAAM,CAAC,UAAU;;CAE5B,CAAC;YAEI,MAAM,SAAS,CAAC,qCAAqC,QAAQ,UAAU,CAAC,CAAC;YACzE,MAAM,kBAAE,CAAC,SAAS,CAAC,uBAAuB,QAAQ,sBAAsB,EAAE,WAAW,CAAC,CAAC;YAGvF,MAAM,SAAS,CAAC,8BAA8B,CAAC,CAAC;YAChD,MAAM,SAAS,CAAC,uCAAuC,QAAQ,SAAS,CAAC,CAAC;YAE1E,eAAM,CAAC,IAAI,CAAC,oCAAoC,QAAQ,WAAW,IAAI,EAAE,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,4BAA4B,CAAC,aAAqB,EAAE,QAAgB;QAChF,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,aAAa;gBACb,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,KAAK;gBACL,MAAM;gBACN,aAAa;gBACb,aAAa;gBACb,UAAU;aACX,CAAC;YAEF,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;gBAC9B,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;gBAC/C,MAAM,SAAS,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;gBAC7C,MAAM,SAAS,CAAC,cAAc,QAAQ,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC,CAAC;gBAClE,MAAM,SAAS,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;YAChD,CAAC;YAGD,MAAM,WAAW,GAAG;;;;wBAIF,QAAQ;;;;;;;;;;;;qBAYX,IAAI,CAAC,gBAAgB;uBACnB,QAAQ;;;QAGvB,CAAC;YAEH,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,CAAC,CAAC;YACvF,MAAM,SAAS,CAAC,cAAc,QAAQ,IAAI,QAAQ,IAAI,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;YAE/G,eAAM,CAAC,IAAI,CAAC,yCAAyC,QAAQ,EAAE,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,wBAAwB,CAAC,QAAgB,EAAE,IAAY;QACnE,IAAI,CAAC;YAEH,MAAM,SAAS,CAAC,0DAA0D,CAAC,CAAC;YAC5E,MAAM,SAAS,CAAC,mEAAmE,CAAC,CAAC;YACrF,MAAM,SAAS,CAAC,mDAAmD,IAAI,6BAA6B,CAAC,CAAC;YACtG,MAAM,SAAS,CAAC,gFAAgF,IAAI,oBAAoB,IAAI,EAAE,CAAC,CAAC;YAEhI,eAAM,CAAC,IAAI,CAAC,yCAAyC,QAAQ,aAAa,IAAI,EAAE,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAE/E,CAAC;IACH,CAAC;IAGO,aAAa,CAAC,IAAY;QAChC,MAAM,UAAU,GAAyD;YACvE,MAAM,EAAE;gBACN,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,GAAG;gBACf,eAAe,EAAE,CAAC;gBAClB,aAAa,EAAE,CAAC;aACjB;YACD,SAAS,EAAE;gBACT,SAAS,EAAE,CAAC;gBACZ,UAAU,EAAE,GAAG;gBACf,eAAe,EAAE,EAAE;gBACnB,aAAa,EAAE,CAAC;aACjB;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,GAAG;gBACf,eAAe,EAAE,EAAE;gBACnB,aAAa,EAAE,EAAE;aAClB;YACD,UAAU,EAAE;gBACV,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,IAAI;gBAChB,eAAe,EAAE,GAAG;gBACpB,aAAa,EAAE,EAAE;aAClB;YACD,KAAK,EAAE;gBACL,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,IAAI;gBAChB,eAAe,EAAE,GAAG;gBACpB,aAAa,EAAE,EAAE;aAClB;SACF,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,SAAS,CAAE,CAAC;IACpD,CAAC;IAGO,KAAK,CAAC,oBAAoB;QAEhC,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;IAC9D,CAAC;IAGD,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,OAAiC;QACvE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAC7E,MAAM,SAAS,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjG,MAAM,SAAS,GAAG,iBAAiB,MAAM,SAAS,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YAGjF,MAAM,SAAS,CAAC,iBAAiB,SAAS,EAAE,CAAC,CAAC;YAC9C,MAAM,SAAS,CAAC,mBAAmB,MAAM,SAAS,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;YAGzE,MAAM,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAEzE,MAAM,WAAW,GAA6B;gBAC5C,EAAE,EAAE,KAAK;gBACT,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;gBACjD,SAAS,EAAE,GAAG,SAAS,cAAc;gBACrC,SAAS;gBACT,MAAM,EAAE,SAAS;gBACjB,WAAW,EAAE,KAAK;gBAClB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACrC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,IAAI,cAAc,MAAM,EAAE,CAAC,CAAC;YACxE,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,2BAA2B,CAAC,SAAiB,EAAE,IAAY,EAAE,OAAiC;QAC1G,IAAI,CAAC;YACH,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,gBAAgB;oBACnB,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,YAAY;oBACf,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;oBAClC,MAAM;gBACR,KAAK,aAAa;oBAChB,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;oBAC/C,MAAM;YACV,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mBAAmB,IAAI,eAAe,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,kBAAkB,CAAC,SAAiB;QAChD,MAAM,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAgCd,CAAC;QAEL,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE,SAAS,CAAC,CAAC;QAClE,MAAM,SAAS,CAAC,iBAAiB,SAAS,kBAAkB,CAAC,CAAC;IAChE,CAAC;IAGO,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,OAAiC;QAC/E,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,+CAA+C;YAC5D,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACP,KAAK,EAAE,OAAO,CAAC,aAAa,IAAI,eAAe;gBAC/C,KAAK,EAAE,OAAO,CAAC,aAAa,IAAI,aAAa;aAC9C;YACD,YAAY,EAAE;gBACZ,OAAO,EAAE,SAAS;aACnB;SACF,CAAC;QAEF,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;;CAiBnB,CAAC;QAEE,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/F,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;IAChE,CAAC;IAGO,KAAK,CAAC,WAAW,CAAC,SAAiB;QACzC,MAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BlB,CAAC;QAEA,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,CAAC;IAClE,CAAC;IAGO,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,OAAiC;QAEhF,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAG9C,MAAM,UAAU,GAAG;;;;;;;;;;;;;gBAaP,OAAO,CAAC,IAAI;;;;;;;;;CAS3B,CAAC;QAEE,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC;IACjE,CAAC;IAGD,KAAK,CAAC,OAAO,CAAC,OAAe;QAE3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,MAAc;QAErD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB;QAErC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,QAAa;QAEpD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAe,EAAE,MAAc,EAAE,OAAgB;QAEtE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,KAAa;QAEvD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF;AAED,IAAI,oBAA0C,CAAC;AAE/C,SAAgB,uBAAuB;IACrC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC1B,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACpD,CAAC;IACD,OAAO,oBAAoB,CAAC;AAC9B,CAAC"}