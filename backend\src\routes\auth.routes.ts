import { FastifyInstance } from 'fastify';
import {
  register<PERSON><PERSON>roller,
  loginController,
  getCurrent<PERSON>ser<PERSON>ontroller,
  getProfileController,
  changePasswordController,
  logoutController,
  verifyTokenController,
} from '../controllers/auth.controller';
import { authMiddleware } from '../middleware/auth';

export async function authRoutes(fastify: FastifyInstance): Promise<void> {
  // Public routes (no authentication required)
  fastify.post('/register', registerController);
  fastify.post('/login', loginController);

  // Protected routes (authentication required)
  fastify.register(async function (fastify) {
    // Add auth middleware to all routes in this context
    fastify.addHook('preHandler', authMiddleware);

    fastify.get('/me', getCurrentUserController);
    fastify.get('/profile', getProfileController);
    fastify.post('/change-password', changePasswordController);
    fastify.post('/logout', logoutController);
    fastify.get('/verify', verifyTokenController);
  });
}
