export declare class AppError extends Error {
    readonly statusCode: number;
    readonly code: string;
    readonly isOperational: boolean;
    readonly details?: any;
    constructor(message: string, statusCode?: number, code?: string, isOperational?: boolean, details?: any);
}
export declare class ValidationError extends AppError {
    constructor(message: string, details?: any);
}
export declare class AuthenticationError extends AppError {
    constructor(message?: string);
}
export declare class AuthorizationError extends AppError {
    constructor(message?: string);
}
export declare class NotFoundError extends AppError {
    constructor(message?: string);
}
export declare class ConflictError extends AppError {
    constructor(message?: string);
}
export declare class RateLimitError extends AppError {
    constructor(message?: string);
}
export declare class ExternalServiceError extends AppError {
    constructor(service: string, message?: string);
}
export declare class DatabaseError extends AppError {
    constructor(message?: string);
}
export declare const createError: {
    validation: (message: string, details?: any) => ValidationError;
    authentication: (message?: string) => AuthenticationError;
    authorization: (message?: string) => AuthorizationError;
    notFound: (message?: string) => NotFoundError;
    conflict: (message?: string) => ConflictError;
    rateLimit: (message?: string) => RateLimitError;
    externalService: (service: string, message?: string) => ExternalServiceError;
    database: (message?: string) => DatabaseError;
    internal: (message: string, details?: any) => AppError;
};
export declare function isOperationalError(error: Error): boolean;
export declare function logError(error: Error, context?: any): void;
export declare function formatErrorResponse(error: Error): {
    success: boolean;
    error: {
        code: string;
        message: string;
        details: any;
    };
};
export declare function asyncHandler<T extends any[], R>(fn: (...args: T) => Promise<R>): (...args: T) => Promise<R>;
export declare class CircuitBreaker {
    private readonly threshold;
    private readonly resetTimeout;
    private failures;
    private lastFailureTime;
    private state;
    constructor(threshold?: number, resetTimeout?: number);
    execute<T>(operation: () => Promise<T>): Promise<T>;
    private onSuccess;
    private onFailure;
    getState(): string;
    getFailures(): number;
}
export declare function retry<T>(operation: () => Promise<T>, maxAttempts?: number, delay?: number, backoff?: number): Promise<T>;
export declare function handleUncaughtException(error: Error): void;
export declare function handleUnhandledRejection(reason: any, _promise: Promise<any>): void;
//# sourceMappingURL=errors.d.ts.map