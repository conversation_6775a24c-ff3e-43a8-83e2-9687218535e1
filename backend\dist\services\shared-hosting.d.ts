export interface SharedHostingUser {
    id: string;
    user_id: string;
    username: string;
    linux_username: string;
    home_directory: string;
    plan: string;
    status: 'active' | 'suspended' | 'disabled';
    server_id: string;
    server_ip: string;
    port: number;
    ssh_port: number;
    ftp_port: number;
    resource_limits: {
        cpu_quota: number;
        memory_max: number;
        bandwidth_limit: number;
        storage_limit: number;
    };
    usage: {
        cpu_usage: number;
        memory_usage: number;
        bandwidth_used: number;
        storage_used: number;
    };
    created_at: string;
    last_login?: string;
    applications: SharedHostingApplication[];
}
export interface SharedHostingApplication {
    id: string;
    name: string;
    type: 'static-website' | 'web-service';
    framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
    domain?: string;
    subdomain: string;
    directory: string;
    status: 'running' | 'stopped' | 'building' | 'error';
    port?: number;
    ssl_enabled: boolean;
    created_at: string;
    last_deployed?: string;
}
export interface CreateSharedUserRequest {
    user_id: string;
    username: string;
    plan: string;
    server_id?: string;
}
export interface CreateApplicationRequest {
    name: string;
    type: 'static-website' | 'web-service';
    framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
    domain?: string;
    git_repo?: string;
    build_command?: string;
    start_command?: string;
}
export interface FileStructureItem {
    name: string;
    type: 'file' | 'directory';
    size?: number;
    permissions: string;
    owner: string;
    modified: string;
    path: string;
}
declare class SharedHostingService {
    private readonly SHARED_SERVER_IP;
    private readonly BASE_PORT;
    private readonly SSH_BASE_PORT;
    private readonly FTP_BASE_PORT;
    createUser(userData: CreateSharedUserRequest): Promise<SharedHostingUser>;
    private createLinuxUser;
    private setupResourceLimits;
    private createUserDirectoryStructure;
    private setupBandwidthThrottling;
    private getPlanLimits;
    private getNextAvailablePort;
    private getNextAvailableSSHPort;
    private getNextAvailableFTPPort;
    createApplication(userId: string, appData: CreateApplicationRequest): Promise<SharedHostingApplication>;
    private setupApplicationEnvironment;
    private setupStaticWebsite;
    private getFrameworkInfo;
    private createReactFiles;
    private createVueFiles;
    private createAngularFiles;
    private setupWebService;
    private installFrameworkRuntime;
    private installNodeJS;
    private installPython;
    private installRust;
    private installPHP;
    private installGo;
    private installJava;
    private installDotNet;
    private installRuby;
    private setupNodeJSService;
    private setupPythonService;
    private setupRustService;
    private setupPHPService;
    private setupGoService;
    private setupJavaService;
    private setupDotNetService;
    private setupRubyService;
    getUser(_userId: string): Promise<SharedHostingUser | null>;
    deployApplication(_userId: string, _appId: string): Promise<any>;
    getServerStatus(_serverId: string): Promise<any>;
    listServerUsers(_serverId: string, _options: any): Promise<SharedHostingUser[]>;
    toggleUserStatus(_userId: string, action: string, _reason?: string): Promise<any>;
    getUserFileStructure(_userId: string, _path: string): Promise<FileStructureItem[]>;
}
export declare function getSharedHostingService(): SharedHostingService;
export {};
//# sourceMappingURL=shared-hosting.d.ts.map