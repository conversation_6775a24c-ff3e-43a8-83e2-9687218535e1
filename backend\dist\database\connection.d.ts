import { Db, Collection, Document } from 'mongodb';
export declare class DatabaseConnection {
    private client;
    private database;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getDatabase(): Db;
    getCollection<T extends Document = Document>(name: string): Collection<T>;
    private extractDatabaseName;
    createIndexes(): Promise<void>;
}
export declare const databaseConnection: DatabaseConnection;
//# sourceMappingURL=connection.d.ts.map