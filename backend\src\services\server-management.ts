import { logger } from '../utils/logger';
import { execAsync } from '../utils/exec';

export interface ServerInfo {
  id: string;
  ip_address: string;
  hostname: string;
  region: string;
  plan: string;
  os: string;
  status: 'active' | 'pending' | 'suspended' | 'installing';
  cpu_count: number;
  ram_mb: number;
  disk_gb: number;
  bandwidth_gb: number;
  monthly_cost: number;
  created_at: string;
  users_count?: number;
  max_users?: number;
  load_average?: number[];
  disk_usage_percent?: number;
  memory_usage_percent?: number;
  network_usage?: {
    incoming_mb: number;
    outgoing_mb: number;
  };
}

export interface ServerMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_in: number;
  network_out: number;
  load_average: number[];
  uptime: number;
  active_users: number;
}

export interface ProvisionServerRequest {
  plan: string;
  region: string;
  hostname: string;
  os?: string;
  user_id: string;
  tier: 'dedicated' | 'enterprise';
}

class ServerManagementService {
  private readonly SHARED_SERVER_IP = '*************';
  private readonly VULTR_API_KEY = process.env.VULTR_API_KEY;
  private readonly VULTR_API_BASE = 'https://api.vultr.com/v2';

  // Get information about the existing shared server
  async getSharedServerInfo(): Promise<ServerInfo> {
    try {
      logger.info('Getting shared server information');
      
      // Get server metrics from the actual server
      const metrics = await this.getServerMetrics(this.SHARED_SERVER_IP);
      
      return {
        id: 'shared-server-main',
        ip_address: this.SHARED_SERVER_IP,
        hostname: 'achidas-shared-1',
        region: 'JNB', // Johannesburg
        plan: 'vhf-1c-1gb',
        os: 'Ubuntu 25.04',
        status: 'active',
        cpu_count: 1,
        ram_mb: 1024,
        disk_gb: 25,
        bandwidth_gb: 1024,
        monthly_cost: 5.00,
        created_at: '2024-01-01T00:00:00Z',
        users_count: await this.getActiveUsersCount(),
        max_users: 50,
        load_average: metrics.load_average,
        disk_usage_percent: metrics.disk_usage,
        memory_usage_percent: metrics.memory_usage,
        network_usage: {
          incoming_mb: metrics.network_in,
          outgoing_mb: metrics.network_out
        }
      };
    } catch (error) {
      logger.error('Failed to get shared server info:', error);
      throw error;
    }
  }

  // Get real-time server metrics
  async getServerMetrics(serverIp: string): Promise<ServerMetrics> {
    try {
      // Execute commands on the server to get metrics
      const cpuUsage = await this.getCPUUsage(serverIp);
      const memoryUsage = await this.getMemoryUsage(serverIp);
      const diskUsage = await this.getDiskUsage(serverIp);
      const networkStats = await this.getNetworkStats(serverIp);
      const loadAverage = await this.getLoadAverage(serverIp);
      const uptime = await this.getUptime(serverIp);
      const activeUsers = await this.getActiveUsersCount();

      return {
        cpu_usage: cpuUsage,
        memory_usage: memoryUsage,
        disk_usage: diskUsage,
        network_in: networkStats.incoming,
        network_out: networkStats.outgoing,
        load_average: loadAverage,
        uptime: uptime,
        active_users: activeUsers
      };
    } catch (error) {
      logger.error(`Failed to get metrics for server ${serverIp}:`, error);
      throw error;
    }
  }

  // Get CPU usage percentage
  private async getCPUUsage(serverIp: string): Promise<number> {
    try {
      const result = await execAsync(`ssh root@${serverIp} "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}'"`);
      return parseFloat(result.stdout.trim()) || 0;
    } catch (error) {
      logger.warn('Failed to get CPU usage:', error);
      return 0;
    }
  }

  // Get memory usage percentage
  private async getMemoryUsage(serverIp: string): Promise<number> {
    try {
      const result = await execAsync(`ssh root@${serverIp} "free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'"`);
      return parseFloat(result.stdout.trim()) || 0;
    } catch (error) {
      logger.warn('Failed to get memory usage:', error);
      return 0;
    }
  }

  // Get disk usage percentage
  private async getDiskUsage(serverIp: string): Promise<number> {
    try {
      const result = await execAsync(`ssh root@${serverIp} "df -h / | awk 'NR==2{print $5}' | sed 's/%//g'"`);
      return parseFloat(result.stdout.trim()) || 0;
    } catch (error) {
      logger.warn('Failed to get disk usage:', error);
      return 0;
    }
  }

  // Get network statistics
  private async getNetworkStats(serverIp: string): Promise<{ incoming: number; outgoing: number }> {
    try {
      const result = await execAsync(`ssh root@${serverIp} "cat /proc/net/dev | grep eth0 | awk '{print $2, $10}'"`);
      const [incoming, outgoing] = result.stdout.trim().split(' ').map(val => parseInt(val) / (1024 * 1024)); // Convert to MB
      return { incoming: incoming || 0, outgoing: outgoing || 0 };
    } catch (error) {
      logger.warn('Failed to get network stats:', error);
      return { incoming: 0, outgoing: 0 };
    }
  }

  // Get load average
  private async getLoadAverage(serverIp: string): Promise<number[]> {
    try {
      const result = await execAsync(`ssh root@${serverIp} "uptime | awk -F'load average:' '{print $2}' | sed 's/,//g'"`);
      return result.stdout.trim().split(' ').map(val => parseFloat(val.trim())).filter(val => !isNaN(val));
    } catch (error) {
      logger.warn('Failed to get load average:', error);
      return [0, 0, 0];
    }
  }

  // Get server uptime in seconds
  private async getUptime(serverIp: string): Promise<number> {
    try {
      const result = await execAsync(`ssh root@${serverIp} "cat /proc/uptime | awk '{print $1}'"`);
      return parseFloat(result.stdout.trim()) || 0;
    } catch (error) {
      logger.warn('Failed to get uptime:', error);
      return 0;
    }
  }

  // Get count of active users on shared server
  private async getActiveUsersCount(): Promise<number> {
    try {
      const result = await execAsync(`ssh root@${this.SHARED_SERVER_IP} "who | wc -l"`);
      return parseInt(result.stdout.trim()) || 0;
    } catch (error) {
      logger.warn('Failed to get active users count:', error);
      return 0;
    }
  }

  // Provision new dedicated/enterprise server via Vultr API
  async provisionServer(request: ProvisionServerRequest): Promise<ServerInfo> {
    try {
      if (!this.VULTR_API_KEY) {
        throw new Error('Vultr API key not configured');
      }

      logger.info(`Provisioning ${request.tier} server for user: ${request.user_id}`);

      const vultrResponse = await fetch(`${this.VULTR_API_BASE}/instances`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.VULTR_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          region: request.region,
          plan: request.plan,
          os_id: 387, // Ubuntu 22.04 LTS
          hostname: request.hostname,
          label: `${request.tier}-${request.user_id}`,
          tag: `achidas-${request.tier}`,
          user_data: this.generateUserData(request.tier),
          enable_ipv6: false,
          enable_private_network: false,
          auto_backups: true
        })
      });

      if (!vultrResponse.ok) {
        const error = await vultrResponse.text();
        throw new Error(`Vultr API error: ${error}`);
      }

      const vultrData = await vultrResponse.json();
      
      const serverInfo: ServerInfo = {
        id: vultrData.instance.id,
        ip_address: vultrData.instance.main_ip || 'pending',
        hostname: request.hostname,
        region: request.region,
        plan: request.plan,
        os: 'Ubuntu 22.04 LTS',
        status: 'installing',
        cpu_count: this.getPlanSpecs(request.plan).cpu,
        ram_mb: this.getPlanSpecs(request.plan).ram,
        disk_gb: this.getPlanSpecs(request.plan).disk,
        bandwidth_gb: this.getPlanSpecs(request.plan).bandwidth,
        monthly_cost: this.getPlanSpecs(request.plan).cost,
        created_at: new Date().toISOString(),
        users_count: 0,
        max_users: 1
      };

      logger.info(`Server provisioned successfully: ${serverInfo.id}`);
      return serverInfo;
    } catch (error) {
      logger.error('Failed to provision server:', error);
      throw error;
    }
  }

  // Generate cloud-init user data for server setup
  private generateUserData(tier: string): string {
    return `#!/bin/bash
# Achidas ${tier} server setup
apt-get update
apt-get install -y curl wget git htop nginx ufw fail2ban

# Configure firewall
ufw allow ssh
ufw allow http
ufw allow https
ufw --force enable

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Create achidas user
useradd -m -s /bin/bash achidas
usermod -aG sudo achidas
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Setup monitoring
echo "Server setup completed at $(date)" > /var/log/achidas-setup.log
`;
  }

  // Get plan specifications
  private getPlanSpecs(plan: string): { cpu: number; ram: number; disk: number; bandwidth: number; cost: number } {
    const specs: Record<string, any> = {
      'vc2-1c-1gb': { cpu: 1, ram: 1024, disk: 25, bandwidth: 1024, cost: 5.00 },
      'vc2-2c-4gb': { cpu: 2, ram: 4096, disk: 80, bandwidth: 3072, cost: 17.28 },
      'vc2-4c-8gb': { cpu: 4, ram: 8192, disk: 160, bandwidth: 4096, cost: 34.56 },
      'vc2-8c-16gb': { cpu: 8, ram: 16384, disk: 320, bandwidth: 5120, cost: 68.40 }
    };
    
    return specs[plan] || specs['vc2-1c-1gb'];
  }

  // List all servers (shared + dedicated/enterprise)
  async listAllServers(): Promise<ServerInfo[]> {
    try {
      const servers: ServerInfo[] = [];
      
      // Add shared server
      const sharedServer = await this.getSharedServerInfo();
      servers.push(sharedServer);
      
      // Get dedicated/enterprise servers from Vultr
      if (this.VULTR_API_KEY) {
        const vultrServers = await this.getVultrServers();
        servers.push(...vultrServers);
      }
      
      return servers;
    } catch (error) {
      logger.error('Failed to list servers:', error);
      throw error;
    }
  }

  // Get servers from Vultr API
  private async getVultrServers(): Promise<ServerInfo[]> {
    try {
      const response = await fetch(`${this.VULTR_API_BASE}/instances`, {
        headers: {
          'Authorization': `Bearer ${this.VULTR_API_KEY}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch Vultr servers');
      }

      const data = await response.json();
      
      return data.instances
        .filter((instance: any) => instance.tag?.startsWith('achidas-'))
        .map((instance: any) => ({
          id: instance.id,
          ip_address: instance.main_ip,
          hostname: instance.hostname,
          region: instance.region,
          plan: instance.plan,
          os: instance.os,
          status: instance.server_status === 'ok' ? 'active' : 'pending',
          cpu_count: this.getPlanSpecs(instance.plan).cpu,
          ram_mb: this.getPlanSpecs(instance.plan).ram,
          disk_gb: this.getPlanSpecs(instance.plan).disk,
          bandwidth_gb: this.getPlanSpecs(instance.plan).bandwidth,
          monthly_cost: this.getPlanSpecs(instance.plan).cost,
          created_at: instance.date_created,
          users_count: 1,
          max_users: 1
        }));
    } catch (error) {
      logger.error('Failed to get Vultr servers:', error);
      return [];
    }
  }
}

// Singleton instance
let serverManagementService: ServerManagementService;

export function getServerManagementService(): ServerManagementService {
  if (!serverManagementService) {
    serverManagementService = new ServerManagementService();
  }
  return serverManagementService;
}
