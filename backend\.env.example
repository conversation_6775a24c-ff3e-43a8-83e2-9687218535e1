# Achidas Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=mongodb://localhost:27017/achidas

# =============================================================================
# AUTHENTICATION
# =============================================================================
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=7d

# =============================================================================
# VULTR API CONFIGURATION
# =============================================================================
VULTR_API_KEY=your_vultr_api_key_here
VULTR_API_BASE_URL=https://api.vultr.com/v2

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_PRETTY=true

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGIN=http://localhost:3001,http://localhost:5173
CORS_CREDENTIALS=true

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
JAEGER_ENDPOINT=http://localhost:14268/api/traces
METRICS_PORT=9090

# =============================================================================
# CIRCUIT BREAKER CONFIGURATION
# =============================================================================
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT_SECONDS=60
