import { FastifyInstance } from 'fastify';
import {
  createApplicationController,
  getApplicationsController,
  getApplicationController,
  updateApplicationController,
  deleteApplicationController,
  getApplicationStatsController,
  deployApplicationController,
  stopApplicationController,
} from '../controllers/application.controller';
import { authMiddleware } from '../middleware/auth';

export async function applicationRoutes(fastify: FastifyInstance): Promise<void> {
  // All application routes require authentication
  fastify.addHook('preHandler', authMiddleware);

  // Application CRUD operations
  fastify.post('/', createApplicationController);
  fastify.get('/', getApplicationsController);
  fastify.get('/stats', getApplicationStatsController);
  fastify.get('/:id', getApplicationController);
  fastify.put('/:id', updateApplicationController);
  fastify.delete('/:id', deleteApplicationController);

  // Application management operations
  fastify.post('/:id/deploy', deployApplicationController);
  fastify.post('/:id/stop', stopApplicationController);
}
