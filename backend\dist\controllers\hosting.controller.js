"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getHostingPlansController = getHostingPlansController;
exports.getHostingPlanController = getHostingPlanController;
exports.getRecommendedPlanController = getRecommendedPlanController;
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
const HOSTING_PLANS = [
    {
        tier: 'Shared',
        plan_name: 'free',
        description: 'Free shared hosting for students and personal projects',
        features: [
            '2% CPU allocation',
            '128MB RAM',
            '1GB Storage',
            '5GB Bandwidth',
            'Basic SSL',
            'Community Support'
        ],
        cpu_allocation: 2,
        memory_allocation: 128,
        storage_allocation: 1,
        bandwidth_limit: 5,
        max_concurrent_users: 50,
        price_per_hour: 0,
        price_per_month: 0,
        recommended_for: ['Personal blogs', 'Learning projects', 'Static websites'],
        african_pricing: {
            nigeria_naira: 0,
            south_africa_rand: 0,
            kenya_shilling: 0,
            ghana_cedi: 0
        },
        free_tier: true,
        trial_days: 30
    },
    {
        tier: 'Shared',
        plan_name: 'starter',
        description: 'Ultra-affordable shared hosting for small websites',
        features: [
            '5% CPU allocation',
            '256MB RAM',
            '5GB Storage',
            '25GB Bandwidth',
            'SSL Certificate',
            'Email Support',
            'Daily Backups'
        ],
        cpu_allocation: 5,
        memory_allocation: 256,
        storage_allocation: 5,
        bandwidth_limit: 25,
        max_concurrent_users: 50,
        price_per_hour: 0.003,
        price_per_month: 2.16,
        recommended_for: ['Small businesses', 'Personal websites', 'Blogs'],
        african_pricing: {
            nigeria_naira: 3500,
            south_africa_rand: 40,
            kenya_shilling: 280,
            ghana_cedi: 26
        }
    },
    {
        tier: 'Shared',
        plan_name: 'basic',
        description: 'Shared hosting for growing websites',
        features: [
            '10% CPU allocation',
            '512MB RAM',
            '10GB Storage',
            '50GB Bandwidth',
            'SSL Certificate',
            'Priority Support',
            'Daily Backups',
            'Custom Domain'
        ],
        cpu_allocation: 10,
        memory_allocation: 512,
        storage_allocation: 10,
        bandwidth_limit: 50,
        max_concurrent_users: 50,
        price_per_hour: 0.007,
        price_per_month: 5.00,
        recommended_for: ['Small e-commerce', 'Business websites', 'APIs'],
        african_pricing: {
            nigeria_naira: 8100,
            south_africa_rand: 92,
            kenya_shilling: 650,
            ghana_cedi: 60
        }
    },
    {
        tier: 'Shared',
        plan_name: 'standard',
        description: 'Shared hosting for active websites',
        features: [
            '15% CPU allocation',
            '1GB RAM',
            '20GB Storage',
            '100GB Bandwidth',
            'SSL Certificate',
            'Priority Support',
            'Daily Backups',
            'Multiple Domains',
            'Database Access'
        ],
        cpu_allocation: 15,
        memory_allocation: 1024,
        storage_allocation: 20,
        bandwidth_limit: 100,
        max_concurrent_users: 50,
        price_per_hour: 0.014,
        price_per_month: 10.00,
        recommended_for: ['Active websites', 'Small SaaS', 'Web applications'],
        african_pricing: {
            nigeria_naira: 16200,
            south_africa_rand: 185,
            kenya_shilling: 1300,
            ghana_cedi: 120
        }
    },
    {
        tier: 'Shared',
        plan_name: 'pro',
        description: 'Premium shared hosting for high-traffic sites',
        features: [
            '25% CPU allocation',
            '2GB RAM',
            '50GB Storage',
            '250GB Bandwidth',
            'SSL Certificate',
            '24/7 Support',
            'Daily Backups',
            'Unlimited Domains',
            'Database Access',
            'CDN Integration'
        ],
        cpu_allocation: 25,
        memory_allocation: 2048,
        storage_allocation: 50,
        bandwidth_limit: 250,
        max_concurrent_users: 50,
        price_per_hour: 0.021,
        price_per_month: 15.00,
        recommended_for: ['High-traffic websites', 'E-commerce', 'SaaS applications'],
        african_pricing: {
            nigeria_naira: 24300,
            south_africa_rand: 277,
            kenya_shilling: 1950,
            ghana_cedi: 180
        }
    },
    {
        tier: 'Dedicated',
        plan_name: 'dedicated-small',
        description: 'Dedicated server for growing businesses',
        features: [
            'Full 1 vCPU',
            '1GB RAM',
            '25GB SSD Storage',
            '1TB Bandwidth',
            'Root Access',
            'SSL Certificate',
            '24/7 Support',
            'Daily Backups',
            'Custom OS'
        ],
        cpu_allocation: 100,
        memory_allocation: 1024,
        storage_allocation: 25,
        bandwidth_limit: 1024,
        max_concurrent_users: 1,
        price_per_hour: 0.007,
        price_per_month: 5.00,
        vultr_plan: 'vc2-1c-1gb',
        recommended_for: ['Small businesses', 'Development', 'Testing'],
        african_pricing: {
            nigeria_naira: 8100,
            south_africa_rand: 92,
            kenya_shilling: 650,
            ghana_cedi: 60
        }
    },
    {
        tier: 'Dedicated',
        plan_name: 'dedicated-medium',
        description: 'Dedicated server for medium businesses',
        features: [
            'Full 2 vCPU',
            '4GB RAM',
            '80GB SSD Storage',
            '3TB Bandwidth',
            'Root Access',
            'SSL Certificate',
            '24/7 Support',
            'Daily Backups',
            'Custom OS',
            'Load Balancer'
        ],
        cpu_allocation: 200,
        memory_allocation: 4096,
        storage_allocation: 80,
        bandwidth_limit: 3072,
        max_concurrent_users: 1,
        price_per_hour: 0.024,
        price_per_month: 17.28,
        vultr_plan: 'vc2-2c-4gb',
        recommended_for: ['Medium businesses', 'E-commerce', 'Web applications'],
        african_pricing: {
            nigeria_naira: 27993,
            south_africa_rand: 319,
            kenya_shilling: 2246,
            ghana_cedi: 207
        }
    },
    {
        tier: 'Enterprise',
        plan_name: 'enterprise-standard',
        description: 'Enterprise-grade dedicated server',
        features: [
            'Full 4 vCPU',
            '8GB RAM',
            '160GB SSD Storage',
            '4TB Bandwidth',
            'Root Access',
            'Premium SSL',
            '24/7 Priority Support',
            'Hourly Backups',
            'Custom OS',
            'Load Balancer',
            'DDoS Protection',
            'Monitoring Dashboard'
        ],
        cpu_allocation: 400,
        memory_allocation: 8192,
        storage_allocation: 160,
        bandwidth_limit: 4096,
        max_concurrent_users: 1,
        price_per_hour: 0.048,
        price_per_month: 34.56,
        vultr_plan: 'vc2-4c-8gb',
        recommended_for: ['Large businesses', 'High-traffic applications', 'SaaS platforms'],
        african_pricing: {
            nigeria_naira: 55987,
            south_africa_rand: 639,
            kenya_shilling: 4493,
            ghana_cedi: 415
        }
    },
    {
        tier: 'Enterprise',
        plan_name: 'enterprise-premium',
        description: 'Premium enterprise dedicated server',
        features: [
            'Full 8 vCPU',
            '16GB RAM',
            '320GB SSD Storage',
            '5TB Bandwidth',
            'Root Access',
            'Premium SSL',
            '24/7 Priority Support',
            'Hourly Backups',
            'Custom OS',
            'Load Balancer',
            'DDoS Protection',
            'Monitoring Dashboard',
            'CDN Integration',
            'Auto-scaling'
        ],
        cpu_allocation: 800,
        memory_allocation: 16384,
        storage_allocation: 320,
        bandwidth_limit: 5120,
        max_concurrent_users: 1,
        price_per_hour: 0.095,
        price_per_month: 68.40,
        vultr_plan: 'vc2-8c-16gb',
        recommended_for: ['Enterprise applications', 'High-performance computing', 'Large SaaS'],
        african_pricing: {
            nigeria_naira: 110808,
            south_africa_rand: 1265,
            kenya_shilling: 8892,
            ghana_cedi: 821
        }
    }
];
async function getHostingPlansController(request, reply) {
    try {
        const { tier } = request.query;
        let plans = HOSTING_PLANS;
        if (tier) {
            const tierFilter = tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase();
            plans = HOSTING_PLANS.filter(plan => plan.tier === tierFilter);
        }
        logger_1.logger.info(`Retrieved ${plans.length} hosting plans${tier ? ` for tier: ${tier}` : ''}`);
        return response_1.ResponseHelper.success(reply, plans);
    }
    catch (error) {
        logger_1.logger.error('Get hosting plans controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch hosting plans');
    }
}
async function getHostingPlanController(request, reply) {
    try {
        const { planName } = request.params;
        const plan = HOSTING_PLANS.find(p => p.plan_name === planName);
        if (!plan) {
            return response_1.ResponseHelper.notFound(reply, `Hosting plan '${planName}' not found`);
        }
        logger_1.logger.info(`Retrieved hosting plan: ${planName}`);
        return response_1.ResponseHelper.success(reply, plan);
    }
    catch (error) {
        logger_1.logger.error('Get hosting plan controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch hosting plan');
    }
}
async function getRecommendedPlanController(request, reply) {
    try {
        const { service_type, expected_traffic, budget, tier } = request.query;
        let recommendedPlan;
        if (tier === 'free' || budget === 'free') {
            recommendedPlan = HOSTING_PLANS.find(p => p.free_tier) ?? HOSTING_PLANS[0];
        }
        else if (tier === 'dedicated') {
            recommendedPlan = HOSTING_PLANS.find(p => p.tier === 'Dedicated') ?? HOSTING_PLANS[5];
        }
        else if (tier === 'enterprise') {
            recommendedPlan = HOSTING_PLANS.find(p => p.tier === 'Enterprise') ?? HOSTING_PLANS[7];
        }
        else if (expected_traffic === 'high') {
            recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'pro') ?? HOSTING_PLANS[4];
        }
        else if (expected_traffic === 'medium') {
            recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'standard') ?? HOSTING_PLANS[3];
        }
        else if (service_type === 'static' || expected_traffic === 'low') {
            recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'starter') ?? HOSTING_PLANS[1];
        }
        else {
            recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'basic') ?? HOSTING_PLANS[2];
        }
        logger_1.logger.info(`Recommended plan: ${recommendedPlan.plan_name} for service_type: ${service_type}, traffic: ${expected_traffic}`);
        return response_1.ResponseHelper.success(reply, recommendedPlan);
    }
    catch (error) {
        logger_1.logger.error('Get recommended plan controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to get recommended plan');
    }
}
//# sourceMappingURL=hosting.controller.js.map