"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.hostingRoutes = hostingRoutes;
const hosting_controller_1 = require("../controllers/hosting.controller");
async function hostingRoutes(fastify) {
    fastify.get('/plans', hosting_controller_1.getHostingPlansController);
    fastify.get('/plans/recommend', hosting_controller_1.getRecommendedPlanController);
    fastify.get('/plans/:planName', hosting_controller_1.getHostingPlanController);
}
//# sourceMappingURL=hosting.routes.js.map