"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appConfig = void 0;
const dotenv_1 = require("dotenv");
const zod_1 = require("zod");
(0, dotenv_1.config)();
const envSchema = zod_1.z.object({
    NODE_ENV: zod_1.z.enum(['development', 'staging', 'production']).default('development'),
    PORT: zod_1.z.string().transform(Number).default('3000'),
    HOST: zod_1.z.string().default('0.0.0.0'),
    DATABASE_URL: zod_1.z.string().min(1, 'DATABASE_URL is required'),
    JWT_SECRET: zod_1.z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
    JWT_EXPIRES_IN: zod_1.z.string().default('7d'),
    VULTR_API_KEY: zod_1.z.string().min(1, 'VULTR_API_KEY is required'),
    VULTR_API_BASE_URL: zod_1.z.string().url().default('https://api.vultr.com/v2'),
    LOG_LEVEL: zod_1.z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
    LOG_PRETTY: zod_1.z.string().transform(val => val === 'true').default('false'),
    RATE_LIMIT_MAX: zod_1.z.string().transform(Number).default('100'),
    RATE_LIMIT_WINDOW: zod_1.z.string().transform(Number).default('60000'),
    CORS_ORIGIN: zod_1.z.string().default('http://localhost:3001'),
    CORS_CREDENTIALS: zod_1.z.string().transform(val => val === 'true').default('true'),
    JAEGER_ENDPOINT: zod_1.z.string().url().optional(),
    METRICS_PORT: zod_1.z.string().transform(Number).default('9090'),
    CIRCUIT_BREAKER_FAILURE_THRESHOLD: zod_1.z.string().transform(Number).default('5'),
    CIRCUIT_BREAKER_TIMEOUT_SECONDS: zod_1.z.string().transform(Number).default('60'),
});
const env = envSchema.parse(process.env);
exports.appConfig = {
    server: {
        port: env.PORT,
        host: env.HOST,
        environment: env.NODE_ENV,
        env: env.NODE_ENV,
    },
    database: {
        url: env.DATABASE_URL,
    },
    auth: {
        jwtSecret: env.JWT_SECRET,
        jwtExpiresIn: env.JWT_EXPIRES_IN,
    },
    vultr: {
        apiKey: env.VULTR_API_KEY,
        baseUrl: env.VULTR_API_BASE_URL,
    },
    logging: {
        level: env.LOG_LEVEL,
        pretty: env.LOG_PRETTY,
    },
    rateLimit: {
        max: env.RATE_LIMIT_MAX,
        window: env.RATE_LIMIT_WINDOW,
        timeWindow: env.RATE_LIMIT_WINDOW,
    },
    cors: {
        origin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
        credentials: env.CORS_CREDENTIALS,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    },
    monitoring: {
        jaegerEndpoint: env.JAEGER_ENDPOINT,
        metricsPort: env.METRICS_PORT,
    },
    circuitBreaker: {
        failureThreshold: env.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
        timeoutSeconds: env.CIRCUIT_BREAKER_TIMEOUT_SECONDS,
    },
};
//# sourceMappingURL=index.js.map