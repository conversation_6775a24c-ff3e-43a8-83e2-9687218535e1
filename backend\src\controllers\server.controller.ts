import { FastifyRequest, FastifyReply } from 'fastify';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';
import { getServerManagementService, ProvisionServerRequest } from '../services/server-management';

// Get all servers controller
export async function getServersController(
  request: FastifyRequest<{ Querystring: { include_metrics?: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { include_metrics } = request.query;
    const includeMetrics = include_metrics === 'true';

    const servers = await getServerManagementService().listAllServers();

    // If metrics requested, get detailed metrics for each server
    if (includeMetrics) {
      for (const server of servers) {
        try {
          const metrics = await getServerManagementService().getServerMetrics(server.ip_address);
          server.load_average = metrics.load_average;
          server.disk_usage_percent = metrics.disk_usage;
          server.memory_usage_percent = metrics.memory_usage;
          server.network_usage = {
            incoming_mb: metrics.network_in,
            outgoing_mb: metrics.network_out
          };
        } catch (error) {
          logger.warn(`Failed to get metrics for server ${server.id}:`, error);
        }
      }
    }

    logger.info(`Retrieved ${servers.length} servers${includeMetrics ? ' with metrics' : ''}`);

    return ResponseHelper.success(reply, servers);
  } catch (error) {
    logger.error('Get servers controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch servers');
  }
}

// Get specific server controller
export async function getServerController(
  request: FastifyRequest<{ Params: { serverId: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { serverId } = request.params;

    if (serverId === 'shared-server-main') {
      const server = await getServerManagementService().getSharedServerInfo();
      return ResponseHelper.success(reply, server);
    }

    // For dedicated/enterprise servers, get from Vultr
    const servers = await getServerManagementService().listAllServers();
    const server = servers.find(s => s.id === serverId);

    if (!server) {
      return ResponseHelper.notFound(reply, `Server '${serverId}' not found`);
    }

    logger.info(`Retrieved server: ${serverId}`);

    return ResponseHelper.success(reply, server);
  } catch (error) {
    logger.error('Get server controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch server');
  }
}

// Get server metrics controller
export async function getServerMetricsController(
  request: FastifyRequest<{ Params: { serverId: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { serverId } = request.params;

    // Get server info first
    let serverIp: string;
    if (serverId === 'shared-server-main') {
      const server = await getServerManagementService().getSharedServerInfo();
      serverIp = server.ip_address;
    } else {
      const servers = await getServerManagementService().listAllServers();
      const server = servers.find(s => s.id === serverId);
      if (!server) {
        return ResponseHelper.notFound(reply, `Server '${serverId}' not found`);
      }
      serverIp = server.ip_address;
    }

    const metrics = await getServerManagementService().getServerMetrics(serverIp);

    logger.info(`Retrieved metrics for server: ${serverId}`);

    return ResponseHelper.success(reply, metrics);
  } catch (error) {
    logger.error('Get server metrics controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch server metrics');
  }
}

// Provision new server controller
export async function provisionServerController(
  request: FastifyRequest<{
    Body: {
      plan: string;
      region: string;
      hostname: string;
      os?: string;
      tier: 'dedicated' | 'enterprise';
    }
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const provisionData = request.body;

    // Validate required fields
    if (!provisionData.plan || !provisionData.region || !provisionData.hostname || !provisionData.tier) {
      return ResponseHelper.validationError(
        reply,
        'Missing required fields: plan, region, hostname, tier'
      );
    }

    // Validate tier
    if (!['dedicated', 'enterprise'].includes(provisionData.tier)) {
      return ResponseHelper.validationError(
        reply,
        'Invalid tier. Must be "dedicated" or "enterprise"'
      );
    }

    const provisionRequest: ProvisionServerRequest = {
      ...provisionData,
      user_id: request.user.id
    };

    const server = await getServerManagementService().provisionServer(provisionRequest);

    logger.info(`Provisioned ${provisionData.tier} server: ${server.id} for user: ${request.user.id}`);

    return ResponseHelper.success(reply, server, 201);
  } catch (error) {
    logger.error('Provision server controller error:', error);

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return ResponseHelper.internalError(reply, 'Server provisioning service not configured');
      }
      if (error.message.includes('Vultr API error')) {
        return ResponseHelper.validationError(reply, error.message);
      }
    }

    return ResponseHelper.internalError(reply, 'Failed to provision server');
  }
}

// Get shared server status controller
export async function getSharedServerStatusController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const server = await getServerManagementService().getSharedServerInfo();
    const metrics = await getServerManagementService().getServerMetrics(server.ip_address);

    const status = {
      server_info: server,
      current_metrics: metrics,
      capacity: {
        users_current: server.users_count || 0,
        users_max: server.max_users || 50,
        users_available: (server.max_users || 50) - (server.users_count || 0),
        capacity_percentage: ((server.users_count || 0) / (server.max_users || 50)) * 100
      },
      health: {
        status: metrics.cpu_usage < 80 && metrics.memory_usage < 80 && metrics.disk_usage < 90 ? 'healthy' : 'warning',
        cpu_status: metrics.cpu_usage < 80 ? 'normal' : 'high',
        memory_status: metrics.memory_usage < 80 ? 'normal' : 'high',
        disk_status: metrics.disk_usage < 90 ? 'normal' : 'high'
      }
    };

    logger.info('Retrieved shared server status');

    return ResponseHelper.success(reply, status);
  } catch (error) {
    logger.error('Get shared server status controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch shared server status');
  }
}

// Get server regions controller (for provisioning)
export async function getServerRegionsController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    // African regions with actual country names
    const regions = [
      {
        id: 'jnb',
        name: 'South Africa (Johannesburg)',
        country: 'South Africa',
        city: 'Johannesburg',
        continent: 'Africa',
        available: true
      },
      {
        id: 'ewr',
        name: 'United States (New Jersey)',
        country: 'United States',
        city: 'New Jersey',
        continent: 'North America',
        available: true
      },
      {
        id: 'lhr',
        name: 'United Kingdom (London)',
        country: 'United Kingdom',
        city: 'London',
        continent: 'Europe',
        available: true
      },
      {
        id: 'fra',
        name: 'Germany (Frankfurt)',
        country: 'Germany',
        city: 'Frankfurt',
        continent: 'Europe',
        available: true
      },
      {
        id: 'sgp',
        name: 'Singapore',
        country: 'Singapore',
        city: 'Singapore',
        continent: 'Asia',
        available: true
      }
    ];

    logger.info('Retrieved server regions');

    return ResponseHelper.success(reply, regions);
  } catch (error) {
    logger.error('Get server regions controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch server regions');
  }
}

// Get available server plans controller
export async function getServerPlansController(
  request: FastifyRequest<{ Querystring: { tier?: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { tier } = request.query;

    const plans = [
      {
        id: 'vc2-1c-1gb',
        name: 'Small Dedicated',
        tier: 'dedicated',
        cpu: 1,
        ram_mb: 1024,
        disk_gb: 25,
        bandwidth_gb: 1024,
        price_per_hour: 0.007,
        price_per_month: 5.00,
        recommended_for: ['Small businesses', 'Development', 'Testing']
      },
      {
        id: 'vc2-2c-4gb',
        name: 'Medium Dedicated',
        tier: 'dedicated',
        cpu: 2,
        ram_mb: 4096,
        disk_gb: 80,
        bandwidth_gb: 3072,
        price_per_hour: 0.024,
        price_per_month: 17.28,
        recommended_for: ['Medium businesses', 'E-commerce', 'Web applications']
      },
      {
        id: 'vc2-4c-8gb',
        name: 'Enterprise Standard',
        tier: 'enterprise',
        cpu: 4,
        ram_mb: 8192,
        disk_gb: 160,
        bandwidth_gb: 4096,
        price_per_hour: 0.048,
        price_per_month: 34.56,
        recommended_for: ['Large businesses', 'High-traffic applications', 'SaaS platforms']
      },
      {
        id: 'vc2-8c-16gb',
        name: 'Enterprise Premium',
        tier: 'enterprise',
        cpu: 8,
        ram_mb: 16384,
        disk_gb: 320,
        bandwidth_gb: 5120,
        price_per_hour: 0.095,
        price_per_month: 68.40,
        recommended_for: ['Enterprise applications', 'High-performance computing', 'Large SaaS']
      }
    ];

    let filteredPlans = plans;
    if (tier) {
      filteredPlans = plans.filter(plan => plan.tier === tier);
    }

    logger.info(`Retrieved ${filteredPlans.length} server plans${tier ? ` for tier: ${tier}` : ''}`);

    return ResponseHelper.success(reply, filteredPlans);
  } catch (error) {
    logger.error('Get server plans controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch server plans');
  }
}
