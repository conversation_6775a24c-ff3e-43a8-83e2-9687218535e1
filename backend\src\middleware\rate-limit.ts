import { FastifyInstance } from 'fastify';
import rateLimit from '@fastify/rate-limit';
import { appConfig } from '../config';

export async function registerRateLimit(fastify: FastifyInstance): Promise<void> {
  await fastify.register(rateLimit, {
    max: appConfig.rateLimit.max,
    timeWindow: appConfig.rateLimit.timeWindow,
    errorResponseBuilder: (request, context) => {
      return {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: `Rate limit exceeded, retry in ${Math.round(context.ttl / 1000)} seconds`,
        },
        metadata: {
          timestamp: new Date().toISOString(),
          request_id: request.id,
          rate_limit: {
            max: context.max,
            remaining: (context as any).remaining,
            reset: new Date(Date.now() + context.ttl).toISOString(),
          },
        },
      };
    },
  });
}
