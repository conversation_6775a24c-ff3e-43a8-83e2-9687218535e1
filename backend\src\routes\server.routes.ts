import { FastifyInstance } from 'fastify';
import { authMiddleware } from '../middleware/auth';

// Server controllers
import {
  getServersController,
  getServerController,
  getServerMetricsController,
  provisionServerController,
  getSharedServerStatusController,
  getServerRegionsController,
  getServerPlansController
} from '../controllers/server.controller';

// Hosting controllers
import {
  getHostingPlansController,
  getRecommendedPlanController,
  getHostingPlanController,
  createSharedUserController,
  createApplicationController as createSharedApplicationController
} from '../controllers/hosting.controller';

// Application controllers
import {
  createApplicationController,
  getApplicationsController,
  getApplicationController,
  updateApplicationController,
  deleteApplicationController,
  deployApplicationController,
  stopApplicationController,
  getApplicationStatsController,
} from '../controllers/application.controller';

export async function serverRoutes(fastify: FastifyInstance): Promise<void> {
  // =============================================================================
  // PUBLIC ROUTES (No authentication required)
  // =============================================================================

  // Server information
  fastify.get('/regions', getServerRegionsController);
  fastify.get('/plans', getServerPlansController);

  // Hosting plans
  fastify.get('/hosting/plans', getHostingPlansController);
  fastify.get('/hosting/plans/recommend', getRecommendedPlanController);
  fastify.get('/hosting/plans/:planName', getHostingPlanController);

  // =============================================================================
  // PROTECTED ROUTES (Authentication required)
  // =============================================================================

  fastify.register(async function (fastify) {
    // Add authentication middleware
    fastify.addHook('preHandler', authMiddleware);

    // =========================================================================
    // SERVER MANAGEMENT ROUTES (Dedicated/Enterprise)
    // =========================================================================

    // Server listing and details
    fastify.get('/', getServersController);
    fastify.get('/shared/status', getSharedServerStatusController);
    fastify.get('/:serverId', getServerController);
    fastify.get('/:serverId/metrics', getServerMetricsController);

    // Server provisioning (for dedicated/enterprise tiers)
    fastify.post('/provision', provisionServerController);

    // =========================================================================
    // SHARED HOSTING MANAGEMENT ROUTES
    // =========================================================================

    // Shared hosting user management
    fastify.post('/hosting/shared/users', createSharedUserController);

    // Shared hosting application management
    fastify.post('/hosting/shared/users/:userId/applications', createSharedApplicationController);

    // =========================================================================
    // APPLICATION MANAGEMENT ROUTES (All tiers)
    // =========================================================================

    // Application CRUD operations
    fastify.post('/applications', createApplicationController);
    fastify.get('/applications', getApplicationsController);
    fastify.get('/applications/stats', getApplicationStatsController);
    fastify.get('/applications/:id', getApplicationController);
    fastify.put('/applications/:id', updateApplicationController);
    fastify.delete('/applications/:id', deleteApplicationController);

    // Application deployment operations
    fastify.post('/applications/:id/deploy', deployApplicationController);
    fastify.post('/applications/:id/stop', stopApplicationController);
  });
}
