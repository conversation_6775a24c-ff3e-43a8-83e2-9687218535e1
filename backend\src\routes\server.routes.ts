import { FastifyInstance } from 'fastify';
import { authMiddleware } from '../middleware/auth';
import {
  getServersController,
  getServerController,
  getServerMetricsController,
  provisionServerController,
  getSharedServerStatusController,
  getServerRegionsController,
  getServerPlansController
} from '../controllers/server.controller';

export async function serverRoutes(fastify: FastifyInstance): Promise<void> {
  // Public routes (no authentication required)
  fastify.get('/regions', getServerRegionsController);
  fastify.get('/plans', getServerPlansController);

  // Protected routes (authentication required)
  fastify.register(async function (fastify) {
    // Add authentication middleware
    fastify.addHook('preHandler', authMiddleware);

    // Server management routes
    fastify.get('/', getServersController);
    fastify.get('/shared/status', getSharedServerStatusController);
    fastify.get('/:serverId', getServerController);
    fastify.get('/:serverId/metrics', getServerMetricsController);
    
    // Server provisioning
    fastify.post('/provision', provisionServerController);
  });
}
