export declare const appConfig: {
    readonly server: {
        readonly port: number;
        readonly host: string;
        readonly environment: "development" | "staging" | "production";
    };
    readonly database: {
        readonly url: string;
    };
    readonly auth: {
        readonly jwtSecret: string;
        readonly jwtExpiresIn: string;
    };
    readonly vultr: {
        readonly apiKey: string;
        readonly baseUrl: string;
    };
    readonly logging: {
        readonly level: "fatal" | "error" | "warn" | "info" | "debug" | "trace";
        readonly pretty: boolean;
    };
    readonly rateLimit: {
        readonly max: number;
        readonly window: number;
    };
    readonly cors: {
        readonly origin: string[];
        readonly credentials: boolean;
    };
    readonly monitoring: {
        readonly jaegerEndpoint: string | undefined;
        readonly metricsPort: number;
    };
    readonly circuitBreaker: {
        readonly failureThreshold: number;
        readonly timeoutSeconds: number;
    };
};
export type AppConfig = typeof appConfig;
//# sourceMappingURL=index.d.ts.map