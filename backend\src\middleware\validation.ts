import { FastifyRequest, FastifyReply, preH<PERSON>ler<PERSON>ook<PERSON><PERSON><PERSON> } from 'fastify';
import { z, ZodSchema } from 'zod';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';

export interface ValidationOptions {
  body?: ZodSchema;
  params?: ZodSchema;
  query?: ZodSchema;
  headers?: ZodSchema;
}

export function validateRequest(options: ValidationOptions): preHandlerHookHandler {
  return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const errors: Array<{ field: string; message: string; value?: any }> = [];

      // Validate request body
      if (options.body && request.body) {
        const bodyResult = options.body.safeParse(request.body);
        if (!bodyResult.success) {
          bodyResult.error.errors.forEach(err => {
            errors.push({
              field: `body.${err.path.join('.')}`,
              message: err.message,
              value: err.path.reduce((obj: any, key) => obj?.[key], request.body),
            });
          });
        } else {
          // Replace request body with validated data
          request.body = bodyResult.data;
        }
      }

      // Validate request parameters
      if (options.params && request.params) {
        const paramsResult = options.params.safeParse(request.params);
        if (!paramsResult.success) {
          paramsResult.error.errors.forEach(err => {
            errors.push({
              field: `params.${err.path.join('.')}`,
              message: err.message,
              value: err.path.reduce((obj: any, key) => obj?.[key], request.params),
            });
          });
        } else {
          // Replace request params with validated data
          request.params = paramsResult.data;
        }
      }

      // Validate query parameters
      if (options.query && request.query) {
        const queryResult = options.query.safeParse(request.query);
        if (!queryResult.success) {
          queryResult.error.errors.forEach(err => {
            errors.push({
              field: `query.${err.path.join('.')}`,
              message: err.message,
              value: err.path.reduce((obj: any, key) => obj?.[key], request.query),
            });
          });
        } else {
          // Replace request query with validated data
          request.query = queryResult.data;
        }
      }

      // Validate headers
      if (options.headers && request.headers) {
        const headersResult = options.headers.safeParse(request.headers);
        if (!headersResult.success) {
          headersResult.error.errors.forEach(err => {
            errors.push({
              field: `headers.${err.path.join('.')}`,
              message: err.message,
              value: err.path.reduce((obj: any, key) => obj?.[key], request.headers),
            });
          });
        }
      }

      // If there are validation errors, return them
      if (errors.length > 0) {
        logger.warn('Request validation failed:', {
          url: request.url,
          method: request.method,
          errors,
          requestId: request.id,
        });

        return ResponseHelper.validationError(
          reply,
          'Request validation failed',
          { errors }
        );
      }
    } catch (error) {
      logger.error('Validation middleware error:', error);
      return ResponseHelper.internalError(reply, 'Validation error occurred');
    }
  };
}

// Common validation schemas
export const commonSchemas = {
  // MongoDB ObjectId validation
  objectId: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'),
  
  // Pagination validation
  pagination: z.object({
    page: z.string().regex(/^\d+$/).transform(Number).refine(n => n >= 1, 'Page must be >= 1').optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).refine(n => n >= 1 && n <= 100, 'Limit must be between 1 and 100').optional(),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional(),
  }),

  // Common parameter schemas
  idParam: z.object({
    id: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ID format'),
  }),

  // Common header schemas
  authHeaders: z.object({
    authorization: z.string().regex(/^Bearer .+/, 'Invalid authorization header format'),
  }).partial(),
};

// Validation helper functions
export function validateBody<T>(schema: ZodSchema<T>) {
  return validateRequest({ body: schema });
}

export function validateParams<T>(schema: ZodSchema<T>) {
  return validateRequest({ params: schema });
}

export function validateQuery<T>(schema: ZodSchema<T>) {
  return validateRequest({ query: schema });
}

export function validateHeaders<T>(schema: ZodSchema<T>) {
  return validateRequest({ headers: schema });
}

// Combined validation for common patterns
export function validateIdParam() {
  return validateParams(commonSchemas.idParam);
}

export function validatePagination() {
  return validateQuery(commonSchemas.pagination as any);
}

// Validation for specific routes
export function validateCreateApplication() {
  return validateRequest({
    body: z.object({
      name: z.string().min(1, 'Application name is required').max(100, 'Name too long'),
      description: z.string().optional(),
      type: z.enum(['web_service', 'static_site', 'background_worker', 'cron_job']),
      repository: z.object({
        url: z.string().url('Invalid repository URL'),
        branch: z.string().optional(),
        auto_deploy: z.boolean().optional(),
      }).optional(),
      environment: z.record(z.string()).optional(),
      deployment: z.object({
        build_command: z.string().optional(),
        start_command: z.string().optional(),
        install_command: z.string().optional(),
        dockerfile_path: z.string().optional(),
      }).optional(),
      resources: z.object({
        cpu: z.number().min(0.1).max(32).optional(),
        memory: z.number().min(128).max(32768).optional(),
        disk: z.number().min(1).max(1000).optional(),
      }).optional(),
    }),
  });
}

export function validateUpdateApplication() {
  return validateRequest({
    params: commonSchemas.idParam,
    body: z.object({
      name: z.string().min(1, 'Application name is required').max(100, 'Name too long').optional(),
      description: z.string().optional(),
      repository: z.object({
        url: z.string().url('Invalid repository URL').optional(),
        branch: z.string().optional(),
        auto_deploy: z.boolean().optional(),
      }).optional(),
      environment: z.record(z.string()).optional(),
      deployment: z.object({
        build_command: z.string().optional(),
        start_command: z.string().optional(),
        install_command: z.string().optional(),
        dockerfile_path: z.string().optional(),
      }).optional(),
      resources: z.object({
        cpu: z.number().min(0.1).max(32).optional(),
        memory: z.number().min(128).max(32768).optional(),
        disk: z.number().min(1).max(1000).optional(),
      }).optional(),
    }).partial(),
  });
}

export function validateServerCreation() {
  return validateRequest({
    body: z.object({
      region: z.string().min(1, 'Region is required'),
      plan: z.string().min(1, 'Plan is required'),
      os: z.string().min(1, 'Operating system is required'),
      label: z.string().optional(),
      hostname: z.string().optional(),
      tag: z.string().optional(),
      user_data: z.string().optional(),
      ssh_keys: z.array(z.string()).optional(),
      auto_backups: z.boolean().optional(),
      ddos_protection: z.boolean().optional(),
    }),
  });
}

export function validateBlockStorageCreation() {
  return validateRequest({
    body: z.object({
      region: z.string().min(1, 'Region is required'),
      size_gb: z.number().min(10).max(10000, 'Size must be between 10GB and 10TB'),
      label: z.string().optional(),
    }),
  });
}
