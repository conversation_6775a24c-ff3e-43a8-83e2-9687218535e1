import { FastifyRequest, FastifyReply } from 'fastify';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';
import { getSharedHostingService } from '../services/shared-hosting';

// Shared hosting user types
export interface SharedHostingUser {
  id: string;
  user_id: string;
  username: string;
  linux_username: string;
  home_directory: string;
  plan: string;
  status: 'active' | 'suspended' | 'disabled';
  server_id: string;
  server_ip: string;
  port: number;
  ssh_port: number;
  ftp_port: number;
  resource_limits: {
    cpu_quota: number; // percentage
    memory_max: number; // MB
    bandwidth_limit: number; // GB/month
    storage_limit: number; // GB
  };
  usage: {
    cpu_usage: number;
    memory_usage: number;
    bandwidth_used: number;
    storage_used: number;
  };
  created_at: string;
  last_login?: string;
  applications: SharedHostingApplication[];
}

export interface SharedHostingApplication {
  id: string;
  name: string;
  type: 'static-website' | 'web-service' | 'nodejs-app' | 'php-app';
  domain?: string;
  subdomain: string;
  directory: string;
  status: 'running' | 'stopped' | 'building' | 'error';
  port?: number;
  ssl_enabled: boolean;
  created_at: string;
  last_deployed?: string;
}

export interface CreateSharedUserRequest {
  user_id: string;
  username: string;
  plan: string;
  server_id?: string; // If not provided, will auto-assign to least loaded server
}

export interface CreateApplicationRequest {
  name: string;
  type: 'static-website' | 'web-service' | 'nodejs-app' | 'php-app';
  domain?: string;
  git_repo?: string;
  build_command?: string;
  start_command?: string;
}

// Create shared hosting user controller
export async function createSharedUserController(
  request: FastifyRequest<{ Body: CreateSharedUserRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const userData = request.body;

    // Validate required fields
    if (!userData.user_id || !userData.username || !userData.plan) {
      return ResponseHelper.validationError(
        reply,
        'Missing required fields: user_id, username, plan'
      );
    }

    const sharedUser = await getSharedHostingService().createUser(userData);

    logger.info(`Created shared hosting user: ${userData.username} on server: ${sharedUser.server_id}`);

    return ResponseHelper.success(reply, sharedUser, 201);
  } catch (error) {
    logger.error('Create shared user controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return ResponseHelper.validationError(reply, error.message);
      }
      if (error.message.includes('server capacity')) {
        return ResponseHelper.validationError(reply, 'No available server capacity for new users');
      }
    }
    
    return ResponseHelper.internalError(reply, 'Failed to create shared hosting user');
  }
}

// Get shared hosting user controller
export async function getSharedUserController(
  request: FastifyRequest<{ Params: { userId: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { userId } = request.params;

    const sharedUser = await getSharedHostingService().getUser(userId);

    if (!sharedUser) {
      return ResponseHelper.notFound(reply, 'Shared hosting user not found');
    }

    logger.info(`Retrieved shared hosting user: ${userId}`);

    return ResponseHelper.success(reply, sharedUser);
  } catch (error) {
    logger.error('Get shared user controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch shared hosting user');
  }
}

// Create application for shared user controller
export async function createApplicationController(
  request: FastifyRequest<{ 
    Params: { userId: string };
    Body: CreateApplicationRequest;
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { userId } = request.params;
    const appData = request.body;

    // Validate required fields
    if (!appData.name || !appData.type) {
      return ResponseHelper.validationError(
        reply,
        'Missing required fields: name, type'
      );
    }

    const application = await getSharedHostingService().createApplication(userId, appData);

    logger.info(`Created application: ${appData.name} for user: ${userId}`);

    return ResponseHelper.success(reply, application, 201);
  } catch (error) {
    logger.error('Create application controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return ResponseHelper.notFound(reply, 'Shared hosting user not found');
      }
      if (error.message.includes('already exists')) {
        return ResponseHelper.validationError(reply, error.message);
      }
    }
    
    return ResponseHelper.internalError(reply, 'Failed to create application');
  }
}

// Deploy application controller
export async function deployApplicationController(
  request: FastifyRequest<{ 
    Params: { userId: string; appId: string };
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { userId, appId } = request.params;

    const result = await getSharedHostingService().deployApplication(userId, appId);

    logger.info(`Deployed application: ${appId} for user: ${userId}`);

    return ResponseHelper.success(reply, result);
  } catch (error) {
    logger.error('Deploy application controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return ResponseHelper.notFound(reply, 'Application or user not found');
      }
    }
    
    return ResponseHelper.internalError(reply, 'Failed to deploy application');
  }
}

// Get server status controller
export async function getServerStatusController(
  request: FastifyRequest<{ Params: { serverId: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { serverId } = request.params;

    const status = await getSharedHostingService().getServerStatus(serverId);

    logger.info(`Retrieved server status: ${serverId}`);

    return ResponseHelper.success(reply, status);
  } catch (error) {
    logger.error('Get server status controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch server status');
  }
}

// List all shared users on a server controller
export async function listServerUsersController(
  request: FastifyRequest<{ 
    Params: { serverId: string };
    Querystring: { status?: string; limit?: string; offset?: string };
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { serverId } = request.params;
    const { status, limit = '50', offset = '0' } = request.query;

    const users = await getSharedHostingService().listServerUsers(
      serverId,
      {
        status,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    );

    logger.info(`Retrieved ${users.length} users for server: ${serverId}`);

    return ResponseHelper.success(reply, users);
  } catch (error) {
    logger.error('List server users controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch server users');
  }
}

// Suspend/unsuspend user controller
export async function toggleUserStatusController(
  request: FastifyRequest<{ 
    Params: { userId: string };
    Body: { action: 'suspend' | 'unsuspend' | 'disable'; reason?: string };
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { userId } = request.params;
    const { action, reason } = request.body;

    if (!action || !['suspend', 'unsuspend', 'disable'].includes(action)) {
      return ResponseHelper.validationError(
        reply,
        'Invalid action. Must be: suspend, unsuspend, or disable'
      );
    }

    const result = await getSharedHostingService().toggleUserStatus(userId, action, reason);

    logger.info(`${action} user: ${userId}${reason ? ` - ${reason}` : ''}`);

    return ResponseHelper.success(reply, result);
  } catch (error) {
    logger.error('Toggle user status controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Shared hosting user not found');
    }
    
    return ResponseHelper.internalError(reply, 'Failed to update user status');
  }
}

// Get user file structure controller
export async function getUserFileStructureController(
  request: FastifyRequest<{ 
    Params: { userId: string };
    Querystring: { path?: string };
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { userId } = request.params;
    const { path = '/' } = request.query;

    const fileStructure = await getSharedHostingService().getUserFileStructure(userId, path);

    logger.info(`Retrieved file structure for user: ${userId}, path: ${path}`);

    return ResponseHelper.success(reply, fileStructure);
  } catch (error) {
    logger.error('Get user file structure controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'User or path not found');
    }
    
    return ResponseHelper.internalError(reply, 'Failed to fetch file structure');
  }
}
