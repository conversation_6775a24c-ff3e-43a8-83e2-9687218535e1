import { FastifyRequest, FastifyReply } from 'fastify';
export declare function getServersController(request: FastifyRequest<{
    Querystring: {
        include_metrics?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getServerController(request: FastifyRequest<{
    Params: {
        serverId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getServerMetricsController(request: FastifyRequest<{
    Params: {
        serverId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function provisionServerController(request: FastifyRequest<{
    Body: {
        plan: string;
        region: string;
        hostname: string;
        os?: string;
        tier: 'dedicated' | 'enterprise';
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getSharedServerStatusController(request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getServerRegionsController(request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
export declare function getServerPlansController(request: FastifyRequest<{
    Querystring: {
        tier?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
//# sourceMappingURL=server.controller.d.ts.map