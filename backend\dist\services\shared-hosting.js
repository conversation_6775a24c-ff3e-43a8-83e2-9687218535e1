"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSharedHostingService = getSharedHostingService;
const logger_1 = require("../utils/logger");
const child_process_1 = require("child_process");
const util_1 = require("util");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class SharedHostingService {
    SHARED_SERVER_IP = '*************';
    BASE_PORT = 8000;
    SSH_BASE_PORT = 2200;
    FTP_BASE_PORT = 2100;
    async createUser(userData) {
        try {
            const linuxUsername = `user_${userData.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
            const homeDirectory = `/var/www/${linuxUsername}`;
            const port = await this.getNextAvailablePort();
            const sshPort = await this.getNextAvailableSSHPort();
            const ftpPort = await this.getNextAvailableFTPPort();
            await this.createLinuxUser(linuxUsername, homeDirectory);
            await this.setupResourceLimits(linuxUsername, userData.plan);
            await this.createUserDirectoryStructure(homeDirectory, linuxUsername);
            await this.setupBandwidthThrottling(linuxUsername, port);
            const sharedUser = {
                id: `shared_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                user_id: userData.user_id,
                username: userData.username,
                linux_username: linuxUsername,
                home_directory: homeDirectory,
                plan: userData.plan,
                status: 'active',
                server_id: 'vultr-jnb-shared-01',
                server_ip: this.SHARED_SERVER_IP,
                port,
                ssh_port: sshPort,
                ftp_port: ftpPort,
                resource_limits: this.getPlanLimits(userData.plan),
                usage: {
                    cpu_usage: 0,
                    memory_usage: 0,
                    bandwidth_used: 0,
                    storage_used: 0
                },
                created_at: new Date().toISOString(),
                applications: []
            };
            logger_1.logger.info(`Created shared hosting user: ${linuxUsername} at ${homeDirectory}`);
            return sharedUser;
        }
        catch (error) {
            logger_1.logger.error('Failed to create shared hosting user:', error);
            throw new Error('Failed to create shared hosting user');
        }
    }
    async createLinuxUser(username, homeDirectory) {
        try {
            await execAsync(`sudo adduser --disabled-password --gecos "" --home ${homeDirectory} ${username}`);
            await execAsync(`sudo chmod 700 ${homeDirectory}`);
            await execAsync(`sudo chown ${username}:${username} ${homeDirectory}`);
            await execAsync(`echo "${username}" | sudo tee -a /etc/cron.deny`);
            logger_1.logger.info(`Created Linux user: ${username} with home: ${homeDirectory}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create Linux user ${username}:`, error);
            throw error;
        }
    }
    async setupResourceLimits(username, plan) {
        try {
            const limits = this.getPlanLimits(plan);
            const sliceConfig = `
[Slice]
CPUQuota=${limits.cpu_quota}%
MemoryMax=${limits.memory_max}M
TasksMax=50
`;
            await execAsync(`sudo mkdir -p /etc/systemd/system/${username}.slice.d`);
            await promises_1.default.writeFile(`/etc/systemd/system/${username}.slice.d/limits.conf`, sliceConfig);
            await execAsync('sudo systemctl daemon-reexec');
            await execAsync(`sudo systemctl restart user-$(id -u ${username}).slice`);
            logger_1.logger.info(`Set up resource limits for user: ${username}, plan: ${plan}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup resource limits for ${username}:`, error);
            throw error;
        }
    }
    async createUserDirectoryStructure(homeDirectory, username) {
        try {
            const directories = [
                'public_html',
                'logs',
                'tmp',
                'backups',
                'ssl',
                'apps',
                'apps/static',
                'apps/nodejs',
                'apps/php'
            ];
            for (const dir of directories) {
                const fullPath = path_1.default.join(homeDirectory, dir);
                await execAsync(`sudo mkdir -p ${fullPath}`);
                await execAsync(`sudo chown ${username}:${username} ${fullPath}`);
                await execAsync(`sudo chmod 755 ${fullPath}`);
            }
            const defaultHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>Welcome to ${username}'s Website</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { color: #007bff; font-size: 2em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Achidas Hosting</div>
        <h1>Welcome to your new website!</h1>
        <p>Your shared hosting account is ready. Upload your files to the <code>public_html</code> directory.</p>
        <p>Server: ${this.SHARED_SERVER_IP}</p>
        <p>Username: ${username}</p>
    </div>
</body>
</html>`;
            await promises_1.default.writeFile(path_1.default.join(homeDirectory, 'public_html', 'index.html'), defaultHtml);
            await execAsync(`sudo chown ${username}:${username} ${path_1.default.join(homeDirectory, 'public_html', 'index.html')}`);
            logger_1.logger.info(`Created directory structure for user: ${username}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create directory structure for ${username}:`, error);
            throw error;
        }
    }
    async setupBandwidthThrottling(username, port) {
        try {
            await execAsync(`sudo tc qdisc add dev eth0 root handle 1: htb default 30`);
            await execAsync(`sudo tc class add dev eth0 parent 1: classid 1:1 htb rate 100mbit`);
            await execAsync(`sudo tc class add dev eth0 parent 1:1 classid 1:${port} htb rate 5mbit ceil 10mbit`);
            await execAsync(`sudo tc filter add dev eth0 protocol ip parent 1:0 prio 1 u32 match ip dport ${port} 0xffff flowid 1:${port}`);
            logger_1.logger.info(`Set up bandwidth throttling for user: ${username} on port: ${port}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup bandwidth throttling for ${username}:`, error);
        }
    }
    getPlanLimits(plan) {
        const planLimits = {
            'free': {
                cpu_quota: 2,
                memory_max: 128,
                bandwidth_limit: 5,
                storage_limit: 1
            },
            'starter': {
                cpu_quota: 5,
                memory_max: 256,
                bandwidth_limit: 25,
                storage_limit: 5
            },
            'basic': {
                cpu_quota: 10,
                memory_max: 512,
                bandwidth_limit: 50,
                storage_limit: 10
            },
            'standard': {
                cpu_quota: 15,
                memory_max: 1024,
                bandwidth_limit: 100,
                storage_limit: 20
            },
            'pro': {
                cpu_quota: 25,
                memory_max: 2048,
                bandwidth_limit: 250,
                storage_limit: 50
            }
        };
        return planLimits[plan] ?? planLimits['starter'];
    }
    async getNextAvailablePort() {
        return this.BASE_PORT + Math.floor(Math.random() * 1000);
    }
    async getNextAvailableSSHPort() {
        return this.SSH_BASE_PORT + Math.floor(Math.random() * 100);
    }
    async getNextAvailableFTPPort() {
        return this.FTP_BASE_PORT + Math.floor(Math.random() * 100);
    }
    async createApplication(userId, appData) {
        try {
            const appId = `app_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            const subdomain = `${appData.name.toLowerCase().replace(/[^a-z0-9]/g, '')}-${userId.substring(userId.length - 6)}`;
            const directory = `/var/www/user_${userId}/apps/${appData.type}/${appData.name}`;
            await execAsync(`sudo mkdir -p ${directory}`);
            await execAsync(`sudo chown user_${userId}:user_${userId} ${directory}`);
            await this.setupApplicationEnvironment(directory, appData.type, appData);
            const application = {
                id: appId,
                name: appData.name,
                type: appData.type,
                ...(appData.framework && { framework: appData.framework }),
                ...(appData.domain && { domain: appData.domain }),
                subdomain: `${subdomain}.achidas.com`,
                directory,
                status: 'stopped',
                ssl_enabled: false,
                created_at: new Date().toISOString()
            };
            logger_1.logger.info(`Created application: ${appData.name} for user: ${userId}`);
            return application;
        }
        catch (error) {
            logger_1.logger.error('Failed to create application:', error);
            throw new Error('Failed to create application');
        }
    }
    async setupApplicationEnvironment(directory, type, appData) {
        try {
            if (type === 'static-website') {
                await this.setupStaticWebsite(directory, appData.framework);
            }
            else if (type === 'web-service') {
                await this.setupWebService(directory, appData);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup ${type} environment:`, error);
            throw error;
        }
    }
    async setupStaticWebsite(directory, framework) {
        const frameworkInfo = this.getFrameworkInfo(framework);
        const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>${frameworkInfo.title}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #007bff; font-size: 2.5em; margin-bottom: 10px; }
        .framework-badge { background: ${frameworkInfo.color}; color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9em; }
        .instructions { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">${frameworkInfo.icon}</div>
            <h1>${frameworkInfo.title}</h1>
            <span class="framework-badge">${framework || 'HTML'}</span>
            <p>${frameworkInfo.description}</p>
        </div>
        <div class="instructions">
            <h2>Getting Started</h2>
            ${frameworkInfo.instructions}
        </div>
        <div class="content">
            <h3>File Structure</h3>
            <p>Your files should be organized as follows:</p>
            <ul>
                ${frameworkInfo.structure.map((item) => `<li><strong>${item.name}</strong> - ${item.description}</li>`).join('')}
            </ul>
        </div>
    </div>
</body>
</html>`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'index.html'), indexHtml);
        const directories = ['css', 'js', 'images', 'assets'];
        if (framework === 'react' || framework === 'vue' || framework === 'angular') {
            directories.push('dist', 'build', 'public');
        }
        await execAsync(`sudo mkdir -p ${directory}/{${directories.join(',')}}`);
        if (framework === 'react') {
            await this.createReactFiles(directory);
        }
        else if (framework === 'vue') {
            await this.createVueFiles(directory);
        }
        else if (framework === 'angular') {
            await this.createAngularFiles(directory);
        }
    }
    getFrameworkInfo(framework) {
        const frameworks = {
            html: {
                title: 'Static HTML Website',
                icon: '🌐',
                color: '#e34c26',
                description: 'Upload your HTML, CSS, and JavaScript files here.',
                instructions: `
          <ol>
            <li>Replace this index.html with your own</li>
            <li>Upload your CSS files to the 'css' folder</li>
            <li>Upload your JavaScript files to the 'js' folder</li>
            <li>Upload images to the 'images' folder</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'css/', description: 'Stylesheets' },
                    { name: 'js/', description: 'JavaScript files' },
                    { name: 'images/', description: 'Images and media' }
                ]
            },
            react: {
                title: 'React Application',
                icon: '⚛️',
                color: '#61dafb',
                description: 'Upload your React build files (npm run build output).',
                instructions: `
          <ol>
            <li>Build your React app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'build' or 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Static assets should be in the 'static' folder</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'static/', description: 'Built CSS, JS, and media files' },
                    { name: 'build/', description: 'React build output' },
                    { name: 'public/', description: 'Public assets' }
                ]
            },
            vue: {
                title: 'Vue.js Application',
                icon: '🖖',
                color: '#4fc08d',
                description: 'Upload your Vue.js build files (npm run build output).',
                instructions: `
          <ol>
            <li>Build your Vue app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Assets should be in the appropriate folders</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'dist/', description: 'Vue build output' },
                    { name: 'assets/', description: 'Built assets' },
                    { name: 'css/', description: 'Stylesheets' }
                ]
            },
            angular: {
                title: 'Angular Application',
                icon: '🅰️',
                color: '#dd0031',
                description: 'Upload your Angular build files (ng build output).',
                instructions: `
          <ol>
            <li>Build your Angular app: <code>ng build --prod</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Configure routing for SPA if needed</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'dist/', description: 'Angular build output' },
                    { name: 'assets/', description: 'Static assets' },
                    { name: 'styles/', description: 'Global styles' }
                ]
            }
        };
        return frameworks[framework || 'html'] || frameworks['html'];
    }
    async createReactFiles(directory) {
        const packageJson = {
            name: "react-static-site",
            version: "1.0.0",
            description: "React static site on Achidas hosting",
            scripts: {
                build: "react-scripts build",
                start: "serve -s build"
            }
        };
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async createVueFiles(directory) {
        const packageJson = {
            name: "vue-static-site",
            version: "1.0.0",
            description: "Vue.js static site on Achidas hosting",
            scripts: {
                build: "vue-cli-service build",
                start: "serve -s dist"
            }
        };
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async createAngularFiles(directory) {
        const packageJson = {
            name: "angular-static-site",
            version: "1.0.0",
            description: "Angular static site on Achidas hosting",
            scripts: {
                build: "ng build --prod",
                start: "serve -s dist"
            }
        };
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async setupWebService(directory, appData) {
        const framework = appData.framework || 'nodejs';
        switch (framework) {
            case 'nodejs':
                await this.setupNodeJSService(directory, appData);
                break;
            case 'python':
                await this.setupPythonService(directory, appData);
                break;
            case 'rust':
                await this.setupRustService(directory, appData);
                break;
            case 'php':
                await this.setupPHPService(directory, appData);
                break;
            case 'go':
                await this.setupGoService(directory, appData);
                break;
            case 'java':
                await this.setupJavaService(directory, appData);
                break;
            case 'dotnet':
                await this.setupDotNetService(directory, appData);
                break;
            case 'ruby':
                await this.setupRubyService(directory, appData);
                break;
            default:
                await this.setupNodeJSService(directory, appData);
        }
    }
    async setupNodeJSService(directory, appData) {
        const packageJson = {
            name: appData.name,
            version: "1.0.0",
            description: "Node.js web service on Achidas shared hosting",
            main: "index.js",
            scripts: {
                start: appData.start_command || "node index.js",
                build: appData.build_command || "npm install",
                dev: "nodemon index.js"
            },
            dependencies: {
                express: "^4.18.0",
                cors: "^2.8.5",
                helmet: "^7.0.0"
            },
            devDependencies: {
                nodemon: "^3.0.0"
            }
        };
        const indexJs = `
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: '${appData.name}',
    framework: 'nodejs'
  });
});

// API info endpoint
app.get('/api/info', (req, res) => {
  res.json({
    service: '${appData.name}',
    version: '1.0.0',
    framework: 'nodejs',
    environment: process.env.NODE_ENV || 'development'
  });
});

// Example API endpoint
app.get('/api/hello', (req, res) => {
  res.json({ message: 'Hello from ${appData.name}!' });
});

app.listen(port, () => {
  console.log(\`🚀 ${appData.name} running on port \${port}\`);
});
`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
        await promises_1.default.writeFile(path_1.default.join(directory, 'index.js'), indexJs);
        await promises_1.default.writeFile(path_1.default.join(directory, '.gitignore'), 'node_modules/\n.env\n*.log\n');
    }
    async setupPythonService(directory, appData) {
        const requirementsTxt = `
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
`;
        const mainPy = `
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime

app = FastAPI(title="${appData.name}", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "${appData.name}",
        "framework": "python"
    }

@app.get("/api/info")
async def get_info():
    return {
        "service": "${appData.name}",
        "version": "1.0.0",
        "framework": "python",
        "description": "Python FastAPI service on Achidas"
    }

@app.get("/api/hello")
async def hello():
    return {"message": "Hello from ${appData.name}!"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
`;
        const startSh = `#!/bin/bash
${appData.start_command || 'uvicorn main:app --host 0.0.0.0 --port 8000'}
`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'requirements.txt'), requirementsTxt.trim());
        await promises_1.default.writeFile(path_1.default.join(directory, 'main.py'), mainPy.trim());
        await promises_1.default.writeFile(path_1.default.join(directory, 'start.sh'), startSh.trim());
        await execAsync(`chmod +x ${path_1.default.join(directory, 'start.sh')}`);
    }
    async setupRustService(directory, appData) {
        const cargoToml = `
[package]
name = "${appData.name.replace(/[^a-z0-9_]/g, '_')}"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
`;
        const mainRs = `
use actix_web::{web, App, HttpResponse, HttpServer, Result, middleware::Logger};
use serde::{Deserialize, Serialize};
use chrono::Utc;

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    timestamp: String,
    service: String,
    framework: String,
}

#[derive(Serialize)]
struct InfoResponse {
    service: String,
    version: String,
    framework: String,
    description: String,
}

#[derive(Serialize)]
struct HelloResponse {
    message: String,
}

async fn health() -> Result<HttpResponse> {
    let response = HealthResponse {
        status: "healthy".to_string(),
        timestamp: Utc::now().to_rfc3339(),
        service: "${appData.name}".to_string(),
        framework: "rust".to_string(),
    };
    Ok(HttpResponse::Ok().json(response))
}

async fn info() -> Result<HttpResponse> {
    let response = InfoResponse {
        service: "${appData.name}".to_string(),
        version: "1.0.0".to_string(),
        framework: "rust".to_string(),
        description: "Rust Actix-web service on Achidas".to_string(),
    };
    Ok(HttpResponse::Ok().json(response))
}

async fn hello() -> Result<HttpResponse> {
    let response = HelloResponse {
        message: format!("Hello from {}!", "${appData.name}"),
    };
    Ok(HttpResponse::Ok().json(response))
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    env_logger::init();

    println!("🚀 ${appData.name} starting on port 8080");

    HttpServer::new(|| {
        App::new()
            .wrap(Logger::default())
            .route("/health", web::get().to(health))
            .route("/api/info", web::get().to(info))
            .route("/api/hello", web::get().to(hello))
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'Cargo.toml'), cargoToml.trim());
        await execAsync(`sudo mkdir -p ${directory}/src`);
        await promises_1.default.writeFile(path_1.default.join(directory, 'src', 'main.rs'), mainRs.trim());
    }
    async setupPHPService(directory, appData) {
        const indexPhp = `
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

$request_uri = $_SERVER['REQUEST_URI'];
$method = $_SERVER['REQUEST_METHOD'];

// Simple routing
switch ($request_uri) {
    case '/health':
        echo json_encode([
            'status' => 'healthy',
            'timestamp' => date('c'),
            'service' => '${appData.name}',
            'framework' => 'php'
        ]);
        break;

    case '/api/info':
        echo json_encode([
            'service' => '${appData.name}',
            'version' => '1.0.0',
            'framework' => 'php',
            'php_version' => phpversion()
        ]);
        break;

    case '/api/hello':
        echo json_encode([
            'message' => 'Hello from ${appData.name}!'
        ]);
        break;

    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found']);
}
?>`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'index.php'), indexPhp.trim());
    }
    async setupGoService(directory, appData) {
        const goMod = `
module ${appData.name}

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
)
`;
        const mainGo = `
package main

import (
    "net/http"
    "time"
    "github.com/gin-gonic/gin"
)

type HealthResponse struct {
    Status    string \`json:"status"\`
    Timestamp string \`json:"timestamp"\`
    Service   string \`json:"service"\`
    Framework string \`json:"framework"\`
}

type InfoResponse struct {
    Service     string \`json:"service"\`
    Version     string \`json:"version"\`
    Framework   string \`json:"framework"\`
    Description string \`json:"description"\`
}

func main() {
    r := gin.Default()

    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, HealthResponse{
            Status:    "healthy",
            Timestamp: time.Now().Format(time.RFC3339),
            Service:   "${appData.name}",
            Framework: "go",
        })
    })

    r.GET("/api/info", func(c *gin.Context) {
        c.JSON(http.StatusOK, InfoResponse{
            Service:     "${appData.name}",
            Version:     "1.0.0",
            Framework:   "go",
            Description: "Go Gin service on Achidas",
        })
    })

    r.GET("/api/hello", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "message": "Hello from ${appData.name}!",
        })
    })

    r.Run(":8080")
}
`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'go.mod'), goMod.trim());
        await promises_1.default.writeFile(path_1.default.join(directory, 'main.go'), mainGo.trim());
    }
    async setupJavaService(directory, appData) {
        const readmeMd = `# ${appData.name} - Java Service\n\nJava Spring Boot service setup coming soon...`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'README.md'), readmeMd);
    }
    async setupDotNetService(directory, appData) {
        const readmeMd = `# ${appData.name} - .NET Service\n\n.NET Core service setup coming soon...`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'README.md'), readmeMd);
    }
    async setupRubyService(directory, appData) {
        const readmeMd = `# ${appData.name} - Ruby Service\n\nRuby service setup coming soon...`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'README.md'), readmeMd);
    }
    async getUser(_userId) {
        return null;
    }
    async deployApplication(_userId, _appId) {
        return { status: 'deployed' };
    }
    async getServerStatus(_serverId) {
        return { status: 'healthy' };
    }
    async listServerUsers(_serverId, _options) {
        return [];
    }
    async toggleUserStatus(_userId, action, _reason) {
        return { status: action };
    }
    async getUserFileStructure(_userId, _path) {
        return [];
    }
}
let sharedHostingService;
function getSharedHostingService() {
    if (!sharedHostingService) {
        sharedHostingService = new SharedHostingService();
    }
    return sharedHostingService;
}
//# sourceMappingURL=shared-hosting.js.map