"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSharedHostingService = getSharedHostingService;
const logger_1 = require("../utils/logger");
const child_process_1 = require("child_process");
const util_1 = require("util");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class SharedHostingService {
    SHARED_SERVER_IP = '*************';
    BASE_PORT = 8000;
    SSH_BASE_PORT = 2200;
    FTP_BASE_PORT = 2100;
    async createUser(userData) {
        try {
            const linuxUsername = `user_${userData.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
            const homeDirectory = `/var/www/${linuxUsername}`;
            const port = await this.getNextAvailablePort();
            const sshPort = await this.getNextAvailableSSHPort();
            const ftpPort = await this.getNextAvailableFTPPort();
            await this.createLinuxUser(linuxUsername, homeDirectory);
            await this.setupResourceLimits(linuxUsername, userData.plan);
            await this.createUserDirectoryStructure(homeDirectory, linuxUsername);
            await this.setupBandwidthThrottling(linuxUsername, port);
            const sharedUser = {
                id: `shared_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                user_id: userData.user_id,
                username: userData.username,
                linux_username: linuxUsername,
                home_directory: homeDirectory,
                plan: userData.plan,
                status: 'active',
                server_id: 'vultr-jnb-shared-01',
                server_ip: this.SHARED_SERVER_IP,
                port,
                ssh_port: sshPort,
                ftp_port: ftpPort,
                resource_limits: this.getPlanLimits(userData.plan),
                usage: {
                    cpu_usage: 0,
                    memory_usage: 0,
                    bandwidth_used: 0,
                    storage_used: 0
                },
                created_at: new Date().toISOString(),
                applications: []
            };
            logger_1.logger.info(`Created shared hosting user: ${linuxUsername} at ${homeDirectory}`);
            return sharedUser;
        }
        catch (error) {
            logger_1.logger.error('Failed to create shared hosting user:', error);
            throw new Error('Failed to create shared hosting user');
        }
    }
    async createLinuxUser(username, homeDirectory) {
        try {
            await execAsync(`sudo adduser --disabled-password --gecos "" --home ${homeDirectory} ${username}`);
            await execAsync(`sudo chmod 700 ${homeDirectory}`);
            await execAsync(`sudo chown ${username}:${username} ${homeDirectory}`);
            await execAsync(`echo "${username}" | sudo tee -a /etc/cron.deny`);
            logger_1.logger.info(`Created Linux user: ${username} with home: ${homeDirectory}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create Linux user ${username}:`, error);
            throw error;
        }
    }
    async setupResourceLimits(username, plan) {
        try {
            const limits = this.getPlanLimits(plan);
            const sliceConfig = `
[Slice]
CPUQuota=${limits.cpu_quota}%
MemoryMax=${limits.memory_max}M
TasksMax=50
`;
            await execAsync(`sudo mkdir -p /etc/systemd/system/${username}.slice.d`);
            await promises_1.default.writeFile(`/etc/systemd/system/${username}.slice.d/limits.conf`, sliceConfig);
            await execAsync('sudo systemctl daemon-reexec');
            await execAsync(`sudo systemctl restart user-$(id -u ${username}).slice`);
            logger_1.logger.info(`Set up resource limits for user: ${username}, plan: ${plan}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup resource limits for ${username}:`, error);
            throw error;
        }
    }
    async createUserDirectoryStructure(homeDirectory, username) {
        try {
            const directories = [
                'public_html',
                'logs',
                'tmp',
                'backups',
                'ssl',
                'apps',
                'apps/static',
                'apps/nodejs',
                'apps/php'
            ];
            for (const dir of directories) {
                const fullPath = path_1.default.join(homeDirectory, dir);
                await execAsync(`sudo mkdir -p ${fullPath}`);
                await execAsync(`sudo chown ${username}:${username} ${fullPath}`);
                await execAsync(`sudo chmod 755 ${fullPath}`);
            }
            const defaultHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>Welcome to ${username}'s Website</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { color: #007bff; font-size: 2em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Achidas Hosting</div>
        <h1>Welcome to your new website!</h1>
        <p>Your shared hosting account is ready. Upload your files to the <code>public_html</code> directory.</p>
        <p>Server: ${this.SHARED_SERVER_IP}</p>
        <p>Username: ${username}</p>
    </div>
</body>
</html>`;
            await promises_1.default.writeFile(path_1.default.join(homeDirectory, 'public_html', 'index.html'), defaultHtml);
            await execAsync(`sudo chown ${username}:${username} ${path_1.default.join(homeDirectory, 'public_html', 'index.html')}`);
            logger_1.logger.info(`Created directory structure for user: ${username}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create directory structure for ${username}:`, error);
            throw error;
        }
    }
    async setupBandwidthThrottling(username, port) {
        try {
            await execAsync(`sudo tc qdisc add dev eth0 root handle 1: htb default 30`);
            await execAsync(`sudo tc class add dev eth0 parent 1: classid 1:1 htb rate 100mbit`);
            await execAsync(`sudo tc class add dev eth0 parent 1:1 classid 1:${port} htb rate 5mbit ceil 10mbit`);
            await execAsync(`sudo tc filter add dev eth0 protocol ip parent 1:0 prio 1 u32 match ip dport ${port} 0xffff flowid 1:${port}`);
            logger_1.logger.info(`Set up bandwidth throttling for user: ${username} on port: ${port}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup bandwidth throttling for ${username}:`, error);
        }
    }
    getPlanLimits(plan) {
        const planLimits = {
            'free': {
                cpu_quota: 2,
                memory_max: 128,
                bandwidth_limit: 5,
                storage_limit: 1
            },
            'starter': {
                cpu_quota: 5,
                memory_max: 256,
                bandwidth_limit: 25,
                storage_limit: 5
            },
            'basic': {
                cpu_quota: 10,
                memory_max: 512,
                bandwidth_limit: 50,
                storage_limit: 10
            },
            'standard': {
                cpu_quota: 15,
                memory_max: 1024,
                bandwidth_limit: 100,
                storage_limit: 20
            },
            'pro': {
                cpu_quota: 25,
                memory_max: 2048,
                bandwidth_limit: 250,
                storage_limit: 50
            }
        };
        return planLimits[plan] ?? planLimits['starter'];
    }
    async getNextAvailablePort() {
        return this.BASE_PORT + Math.floor(Math.random() * 1000);
    }
    async getNextAvailableSSHPort() {
        return this.SSH_BASE_PORT + Math.floor(Math.random() * 100);
    }
    async getNextAvailableFTPPort() {
        return this.FTP_BASE_PORT + Math.floor(Math.random() * 100);
    }
    async createApplication(userId, appData) {
        try {
            const appId = `app_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const subdomain = `${appData.name.toLowerCase().replace(/[^a-z0-9]/g, '')}-${userId.substr(-6)}`;
            const directory = `/var/www/user_${userId}/apps/${appData.type}/${appData.name}`;
            await execAsync(`sudo mkdir -p ${directory}`);
            await execAsync(`sudo chown user_${userId}:user_${userId} ${directory}`);
            await this.setupApplicationEnvironment(directory, appData.type, appData);
            const application = {
                id: appId,
                name: appData.name,
                type: appData.type,
                ...(appData.domain && { domain: appData.domain }),
                subdomain: `${subdomain}.achidas.com`,
                directory,
                status: 'stopped',
                ssl_enabled: false,
                created_at: new Date().toISOString()
            };
            logger_1.logger.info(`Created application: ${appData.name} for user: ${userId}`);
            return application;
        }
        catch (error) {
            logger_1.logger.error('Failed to create application:', error);
            throw new Error('Failed to create application');
        }
    }
    async setupApplicationEnvironment(directory, type, appData) {
        try {
            switch (type) {
                case 'static-website':
                    await this.setupStaticWebsite(directory);
                    break;
                case 'nodejs-app':
                    await this.setupNodeJSApp(directory, appData);
                    break;
                case 'php-app':
                    await this.setupPHPApp(directory);
                    break;
                case 'web-service':
                    await this.setupWebService(directory, appData);
                    break;
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup ${type} environment:`, error);
            throw error;
        }
    }
    async setupStaticWebsite(directory) {
        const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>Static Website</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #007bff; font-size: 2.5em; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🌐</div>
            <h1>Your Static Website</h1>
            <p>Upload your HTML, CSS, and JavaScript files here.</p>
        </div>
        <div class="content">
            <h2>Getting Started</h2>
            <ol>
                <li>Replace this index.html with your own</li>
                <li>Upload your CSS files to a 'css' folder</li>
                <li>Upload your JavaScript files to a 'js' folder</li>
                <li>Upload images to an 'images' folder</li>
            </ol>
        </div>
    </div>
</body>
</html>`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'index.html'), indexHtml);
        await execAsync(`sudo mkdir -p ${directory}/{css,js,images}`);
    }
    async setupNodeJSApp(directory, appData) {
        const packageJson = {
            name: appData.name,
            version: "1.0.0",
            description: "Node.js application on Achidas shared hosting",
            main: "index.js",
            scripts: {
                start: appData.start_command || "node index.js",
                build: appData.build_command || "npm install"
            },
            dependencies: {
                express: "^4.18.0"
            }
        };
        const indexJs = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.get('/', (req, res) => {
  res.send(\`
    <h1>🚀 Node.js App on Achidas</h1>
    <p>Your Node.js application is running!</p>
    <p>Port: \${port}</p>
    <p>Environment: \${process.env.NODE_ENV || 'development'}</p>
  \`);
});

app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});
`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
        await promises_1.default.writeFile(path_1.default.join(directory, 'index.js'), indexJs);
    }
    async setupPHPApp(directory) {
        const indexPhp = `
<?php
echo "
<!DOCTYPE html>
<html>
<head>
    <title>PHP Application</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🐘 PHP Application on Achidas</h1>
        <div class='info'>
            <strong>PHP Version:</strong> " . phpversion() . "<br>
            <strong>Server:</strong> " . $_SERVER['SERVER_NAME'] . "<br>
            <strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "
        </div>
        <p>Your PHP application is ready! Upload your PHP files here.</p>
    </div>
</body>
</html>
";
?>`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'index.php'), indexPhp);
    }
    async setupWebService(directory, appData) {
        await this.setupNodeJSApp(directory, appData);
        const apiExample = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.get('/api/info', (req, res) => {
  res.json({
    service: '${appData.name}',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

app.listen(port, () => {
  console.log(\`API service running on port \${port}\`);
});
`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'api.js'), apiExample);
    }
    async getUser(_userId) {
        return null;
    }
    async deployApplication(_userId, _appId) {
        return { status: 'deployed' };
    }
    async getServerStatus(_serverId) {
        return { status: 'healthy' };
    }
    async listServerUsers(_serverId, _options) {
        return [];
    }
    async toggleUserStatus(_userId, action, _reason) {
        return { status: action };
    }
    async getUserFileStructure(_userId, _path) {
        return [];
    }
}
let sharedHostingService;
function getSharedHostingService() {
    if (!sharedHostingService) {
        sharedHostingService = new SharedHostingService();
    }
    return sharedHostingService;
}
//# sourceMappingURL=shared-hosting.js.map