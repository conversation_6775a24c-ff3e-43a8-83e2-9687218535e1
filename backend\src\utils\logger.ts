import pino from 'pino';
import { appConfig } from '../config';

// Create logger instance
export const logger = pino({
  level: appConfig.logging.level,
  transport: appConfig.logging.pretty ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
    },
  } : undefined,
  formatters: {
    level: (label) => {
      return { level: label };
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
  base: {
    env: appConfig.server.environment,
    service: 'achidas-backend',
  },
});

// Request logger for Fastify
export const requestLogger = {
  logger,
  serializers: {
    req: pino.stdSerializers.req,
    res: pino.stdSerializers.res,
  },
};
