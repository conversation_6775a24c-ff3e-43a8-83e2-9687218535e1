import { logger } from '../utils/logger';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

// Shared hosting user types
export interface SharedHostingUser {
  id: string;
  user_id: string;
  username: string;
  linux_username: string;
  home_directory: string;
  plan: string;
  status: 'active' | 'suspended' | 'disabled';
  server_id: string;
  server_ip: string;
  port: number;
  ssh_port: number;
  ftp_port: number;
  resource_limits: {
    cpu_quota: number; // percentage
    memory_max: number; // MB
    bandwidth_limit: number; // GB/month
    storage_limit: number; // GB
  };
  usage: {
    cpu_usage: number;
    memory_usage: number;
    bandwidth_used: number;
    storage_used: number;
  };
  created_at: string;
  last_login?: string;
  applications: SharedHostingApplication[];
}

export interface SharedHostingApplication {
  id: string;
  name: string;
  type: 'static-website' | 'web-service' | 'nodejs-app' | 'php-app';
  domain?: string;
  subdomain: string;
  directory: string;
  status: 'running' | 'stopped' | 'building' | 'error';
  port?: number;
  ssl_enabled: boolean;
  created_at: string;
  last_deployed?: string;
}

export interface CreateSharedUserRequest {
  user_id: string;
  username: string;
  plan: string;
  server_id?: string;
}

export interface CreateApplicationRequest {
  name: string;
  type: 'static-website' | 'web-service' | 'nodejs-app' | 'php-app';
  domain?: string;
  git_repo?: string;
  build_command?: string;
  start_command?: string;
}

export interface FileStructureItem {
  name: string;
  type: 'file' | 'directory';
  size?: number;
  permissions: string;
  owner: string;
  modified: string;
  path: string;
}

class SharedHostingService {
  private readonly SHARED_SERVER_IP = '*************'; // Your Vultr server
  private readonly BASE_PORT = 8000; // Starting port for user applications
  private readonly SSH_BASE_PORT = 2200; // Starting SSH port for users
  private readonly FTP_BASE_PORT = 2100; // Starting FTP port for users

  // Create a new shared hosting user with Linux isolation
  async createUser(userData: CreateSharedUserRequest): Promise<SharedHostingUser> {
    try {
      const linuxUsername = `user_${userData.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
      const homeDirectory = `/var/www/${linuxUsername}`;
      const port = await this.getNextAvailablePort();
      const sshPort = await this.getNextAvailableSSHPort();
      const ftpPort = await this.getNextAvailableFTPPort();

      // Create Linux user with isolated home directory
      await this.createLinuxUser(linuxUsername, homeDirectory);

      // Set up resource limits
      await this.setupResourceLimits(linuxUsername, userData.plan);

      // Create user directory structure
      await this.createUserDirectoryStructure(homeDirectory, linuxUsername);

      // Set up bandwidth throttling
      await this.setupBandwidthThrottling(linuxUsername, port);

      const sharedUser: SharedHostingUser = {
        id: `shared_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        user_id: userData.user_id,
        username: userData.username,
        linux_username: linuxUsername,
        home_directory: homeDirectory,
        plan: userData.plan,
        status: 'active',
        server_id: 'vultr-jnb-shared-01',
        server_ip: this.SHARED_SERVER_IP,
        port,
        ssh_port: sshPort,
        ftp_port: ftpPort,
        resource_limits: this.getPlanLimits(userData.plan),
        usage: {
          cpu_usage: 0,
          memory_usage: 0,
          bandwidth_used: 0,
          storage_used: 0
        },
        created_at: new Date().toISOString(),
        applications: []
      };

      logger.info(`Created shared hosting user: ${linuxUsername} at ${homeDirectory}`);
      return sharedUser;
    } catch (error) {
      logger.error('Failed to create shared hosting user:', error);
      throw new Error('Failed to create shared hosting user');
    }
  }

  // Create Linux user with proper isolation
  private async createLinuxUser(username: string, homeDirectory: string): Promise<void> {
    try {
      // Create user with custom home directory
      await execAsync(`sudo adduser --disabled-password --gecos "" --home ${homeDirectory} ${username}`);
      
      // Set strict permissions (only user can access their directory)
      await execAsync(`sudo chmod 700 ${homeDirectory}`);
      await execAsync(`sudo chown ${username}:${username} ${homeDirectory}`);

      // Disable cron access to prevent background processes
      await execAsync(`echo "${username}" | sudo tee -a /etc/cron.deny`);

      logger.info(`Created Linux user: ${username} with home: ${homeDirectory}`);
    } catch (error) {
      logger.error(`Failed to create Linux user ${username}:`, error);
      throw error;
    }
  }

  // Set up systemd resource limits for CPU and memory
  private async setupResourceLimits(username: string, plan: string): Promise<void> {
    try {
      const limits = this.getPlanLimits(plan);
      
      // Create systemd slice for user
      const sliceConfig = `
[Slice]
CPUQuota=${limits.cpu_quota}%
MemoryMax=${limits.memory_max}M
TasksMax=50
`;

      await execAsync(`sudo mkdir -p /etc/systemd/system/${username}.slice.d`);
      await fs.writeFile(`/etc/systemd/system/${username}.slice.d/limits.conf`, sliceConfig);
      
      // Reload systemd and apply limits
      await execAsync('sudo systemctl daemon-reexec');
      await execAsync(`sudo systemctl restart user-$(id -u ${username}).slice`);

      logger.info(`Set up resource limits for user: ${username}, plan: ${plan}`);
    } catch (error) {
      logger.error(`Failed to setup resource limits for ${username}:`, error);
      throw error;
    }
  }

  // Create user directory structure for web hosting
  private async createUserDirectoryStructure(homeDirectory: string, username: string): Promise<void> {
    try {
      const directories = [
        'public_html',      // Web root
        'logs',            // Application logs
        'tmp',             // Temporary files
        'backups',         // User backups
        'ssl',             // SSL certificates
        'apps',            // Application directories
        'apps/static',     // Static websites
        'apps/nodejs',     // Node.js applications
        'apps/php'         // PHP applications
      ];

      for (const dir of directories) {
        const fullPath = path.join(homeDirectory, dir);
        await execAsync(`sudo mkdir -p ${fullPath}`);
        await execAsync(`sudo chown ${username}:${username} ${fullPath}`);
        await execAsync(`sudo chmod 755 ${fullPath}`);
      }

      // Create default index.html
      const defaultHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>Welcome to ${username}'s Website</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { color: #007bff; font-size: 2em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Achidas Hosting</div>
        <h1>Welcome to your new website!</h1>
        <p>Your shared hosting account is ready. Upload your files to the <code>public_html</code> directory.</p>
        <p>Server: ${this.SHARED_SERVER_IP}</p>
        <p>Username: ${username}</p>
    </div>
</body>
</html>`;

      await fs.writeFile(path.join(homeDirectory, 'public_html', 'index.html'), defaultHtml);
      await execAsync(`sudo chown ${username}:${username} ${path.join(homeDirectory, 'public_html', 'index.html')}`);

      logger.info(`Created directory structure for user: ${username}`);
    } catch (error) {
      logger.error(`Failed to create directory structure for ${username}:`, error);
      throw error;
    }
  }

  // Set up bandwidth throttling using tc (traffic control)
  private async setupBandwidthThrottling(username: string, port: number): Promise<void> {
    try {
      // Set up traffic control for user's port
      await execAsync(`sudo tc qdisc add dev eth0 root handle 1: htb default 30`);
      await execAsync(`sudo tc class add dev eth0 parent 1: classid 1:1 htb rate 100mbit`);
      await execAsync(`sudo tc class add dev eth0 parent 1:1 classid 1:${port} htb rate 5mbit ceil 10mbit`);
      await execAsync(`sudo tc filter add dev eth0 protocol ip parent 1:0 prio 1 u32 match ip dport ${port} 0xffff flowid 1:${port}`);

      logger.info(`Set up bandwidth throttling for user: ${username} on port: ${port}`);
    } catch (error) {
      logger.error(`Failed to setup bandwidth throttling for ${username}:`, error);
      // Don't throw error as this is not critical for basic functionality
    }
  }

  // Get plan resource limits
  private getPlanLimits(plan: string): SharedHostingUser['resource_limits'] {
    const planLimits: Record<string, SharedHostingUser['resource_limits']> = {
      'free': {
        cpu_quota: 2,
        memory_max: 128,
        bandwidth_limit: 5,
        storage_limit: 1
      },
      'starter': {
        cpu_quota: 5,
        memory_max: 256,
        bandwidth_limit: 25,
        storage_limit: 5
      },
      'basic': {
        cpu_quota: 10,
        memory_max: 512,
        bandwidth_limit: 50,
        storage_limit: 10
      },
      'standard': {
        cpu_quota: 15,
        memory_max: 1024,
        bandwidth_limit: 100,
        storage_limit: 20
      },
      'pro': {
        cpu_quota: 25,
        memory_max: 2048,
        bandwidth_limit: 250,
        storage_limit: 50
      }
    };

    return planLimits[plan] ?? planLimits['starter']!;
  }

  // Get next available port for user applications
  private async getNextAvailablePort(): Promise<number> {
    // In production, this should check a database or port registry
    return this.BASE_PORT + Math.floor(Math.random() * 1000);
  }

  private async getNextAvailableSSHPort(): Promise<number> {
    return this.SSH_BASE_PORT + Math.floor(Math.random() * 100);
  }

  private async getNextAvailableFTPPort(): Promise<number> {
    return this.FTP_BASE_PORT + Math.floor(Math.random() * 100);
  }

  // Create application for shared user
  async createApplication(userId: string, appData: CreateApplicationRequest): Promise<SharedHostingApplication> {
    try {
      const appId = `app_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const subdomain = `${appData.name.toLowerCase().replace(/[^a-z0-9]/g, '')}-${userId.substr(-6)}`;
      const directory = `/var/www/user_${userId}/apps/${appData.type}/${appData.name}`;

      // Create application directory
      await execAsync(`sudo mkdir -p ${directory}`);
      await execAsync(`sudo chown user_${userId}:user_${userId} ${directory}`);

      // Set up application based on type
      await this.setupApplicationEnvironment(directory, appData.type, appData);

      const application: SharedHostingApplication = {
        id: appId,
        name: appData.name,
        type: appData.type,
        domain: appData.domain || undefined,
        subdomain: `${subdomain}.achidas.com`,
        directory,
        status: 'stopped',
        ssl_enabled: false,
        created_at: new Date().toISOString()
      };

      logger.info(`Created application: ${appData.name} for user: ${userId}`);
      return application;
    } catch (error) {
      logger.error('Failed to create application:', error);
      throw new Error('Failed to create application');
    }
  }

  // Set up application environment based on type
  private async setupApplicationEnvironment(directory: string, type: string, appData: CreateApplicationRequest): Promise<void> {
    try {
      switch (type) {
        case 'static-website':
          await this.setupStaticWebsite(directory);
          break;
        case 'nodejs-app':
          await this.setupNodeJSApp(directory, appData);
          break;
        case 'php-app':
          await this.setupPHPApp(directory);
          break;
        case 'web-service':
          await this.setupWebService(directory, appData);
          break;
      }
    } catch (error) {
      logger.error(`Failed to setup ${type} environment:`, error);
      throw error;
    }
  }

  // Set up static website environment
  private async setupStaticWebsite(directory: string): Promise<void> {
    const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>Static Website</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #007bff; font-size: 2.5em; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🌐</div>
            <h1>Your Static Website</h1>
            <p>Upload your HTML, CSS, and JavaScript files here.</p>
        </div>
        <div class="content">
            <h2>Getting Started</h2>
            <ol>
                <li>Replace this index.html with your own</li>
                <li>Upload your CSS files to a 'css' folder</li>
                <li>Upload your JavaScript files to a 'js' folder</li>
                <li>Upload images to an 'images' folder</li>
            </ol>
        </div>
    </div>
</body>
</html>`;

    await fs.writeFile(path.join(directory, 'index.html'), indexHtml);
    await execAsync(`sudo mkdir -p ${directory}/{css,js,images}`);
  }

  // Set up Node.js application environment
  private async setupNodeJSApp(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const packageJson = {
      name: appData.name,
      version: "1.0.0",
      description: "Node.js application on Achidas shared hosting",
      main: "index.js",
      scripts: {
        start: appData.start_command || "node index.js",
        build: appData.build_command || "npm install"
      },
      dependencies: {
        express: "^4.18.0"
      }
    };

    const indexJs = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.get('/', (req, res) => {
  res.send(\`
    <h1>🚀 Node.js App on Achidas</h1>
    <p>Your Node.js application is running!</p>
    <p>Port: \${port}</p>
    <p>Environment: \${process.env.NODE_ENV || 'development'}</p>
  \`);
});

app.listen(port, () => {
  console.log(\`Server running on port \${port}\`);
});
`;

    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    await fs.writeFile(path.join(directory, 'index.js'), indexJs);
  }

  // Set up PHP application environment
  private async setupPHPApp(directory: string): Promise<void> {
    const indexPhp = `
<?php
echo "
<!DOCTYPE html>
<html>
<head>
    <title>PHP Application</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🐘 PHP Application on Achidas</h1>
        <div class='info'>
            <strong>PHP Version:</strong> " . phpversion() . "<br>
            <strong>Server:</strong> " . $_SERVER['SERVER_NAME'] . "<br>
            <strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "
        </div>
        <p>Your PHP application is ready! Upload your PHP files here.</p>
    </div>
</body>
</html>
";
?>`;

    await fs.writeFile(path.join(directory, 'index.php'), indexPhp);
  }

  // Set up web service environment
  private async setupWebService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    // Similar to Node.js but optimized for API services
    await this.setupNodeJSApp(directory, appData);
    
    // Add additional API-specific files
    const apiExample = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.get('/api/info', (req, res) => {
  res.json({
    service: '${appData.name}',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

app.listen(port, () => {
  console.log(\`API service running on port \${port}\`);
});
`;

    await fs.writeFile(path.join(directory, 'api.js'), apiExample);
  }

  // Placeholder methods for other functionality
  async getUser(_userId: string): Promise<SharedHostingUser | null> {
    // In production, this would query a database
    return null;
  }

  async deployApplication(_userId: string, _appId: string): Promise<any> {
    // Implementation for deploying applications
    return { status: 'deployed' };
  }

  async getServerStatus(_serverId: string): Promise<any> {
    // Implementation for getting server status
    return { status: 'healthy' };
  }

  async listServerUsers(_serverId: string, _options: any): Promise<SharedHostingUser[]> {
    // Implementation for listing server users
    return [];
  }

  async toggleUserStatus(_userId: string, action: string, _reason?: string): Promise<any> {
    // Implementation for toggling user status
    return { status: action };
  }

  async getUserFileStructure(_userId: string, _path: string): Promise<FileStructureItem[]> {
    // Implementation for getting user file structure
    return [];
  }
}

let sharedHostingService: SharedHostingService;

export function getSharedHostingService(): SharedHostingService {
  if (!sharedHostingService) {
    sharedHostingService = new SharedHostingService();
  }
  return sharedHostingService;
}
