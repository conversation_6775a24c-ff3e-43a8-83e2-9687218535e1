import { logger } from '../utils/logger';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import { Client } from 'ssh2';

const execAsync = promisify(exec);

// SSH Connection utility for shared server management
class SSHConnection {
  private client: Client;
  private config: {
    host: string;
    port: number;
    username: string;
    password?: string;
    privateKey?: Buffer;
    passphrase?: string;
  };

  constructor() {
    this.client = new Client();
    this.config = {
      host: process.env['SHARED_SERVER_IP'] || '*************',
      port: parseInt(process.env['SHARED_SERVER_SSH_PORT'] || '22'),
      username: process.env['SHARED_SERVER_SSH_USER'] || 'root',
      ...(process.env['SHARED_SERVER_SSH_PASSWORD'] && { password: process.env['SHARED_SERVER_SSH_PASSWORD'] }),
      // If using SSH key instead of password:
      // privateKey: process.env['SHARED_SERVER_SSH_KEY_PATH'] ?
      //   require('fs').readFileSync(process.env['SHARED_SERVER_SSH_KEY_PATH']) : undefined,
      // passphrase: process.env['SHARED_SERVER_SSH_PASSPHRASE']
    };
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.on('ready', () => {
        logger.info(`SSH connected to ${this.config.host}`);
        resolve();
      });

      this.client.on('error', (err) => {
        logger.error('SSH connection error:', err);
        reject(err);
      });

      this.client.connect(this.config);
    });
  }

  async executeCommand(command: string): Promise<{ stdout: string; stderr: string }> {
    return new Promise((resolve, reject) => {
      this.client.exec(command, (err, stream) => {
        if (err) {
          reject(err);
          return;
        }

        let stdout = '';
        let stderr = '';

        stream.on('close', (code: number) => {
          if (code !== 0) {
            logger.warn(`Command exited with code ${code}: ${command}`);
          }
          resolve({ stdout, stderr });
        });

        stream.on('data', (data: Buffer) => {
          stdout += data.toString();
        });

        stream.stderr.on('data', (data: Buffer) => {
          stderr += data.toString();
        });
      });
    });
  }

  async disconnect(): Promise<void> {
    this.client.end();
    logger.info('SSH connection closed');
  }
}

// Shared hosting user types
export interface SharedHostingUser {
  id: string;
  user_id: string;
  username: string;
  linux_username: string;
  home_directory: string;
  plan: string;
  status: 'active' | 'suspended' | 'disabled';
  server_id: string;
  server_ip: string;
  port: number;
  ssh_port: number;
  ftp_port: number;
  resource_limits: {
    cpu_quota: number; // percentage
    memory_max: number; // MB
    bandwidth_limit: number; // GB/month
    storage_limit: number; // GB
  };
  usage: {
    cpu_usage: number;
    memory_usage: number;
    bandwidth_used: number;
    storage_used: number;
  };
  created_at: string;
  last_login?: string;
  applications: SharedHostingApplication[];
}

export interface SharedHostingApplication {
  id: string;
  name: string;
  type: 'static-website' | 'web-service';
  framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
  domain?: string;
  subdomain: string;
  directory: string;
  status: 'running' | 'stopped' | 'building' | 'error';
  port?: number;
  ssl_enabled: boolean;
  created_at: string;
  last_deployed?: string;
}

export interface CreateSharedUserRequest {
  user_id: string;
  username: string;
  plan: string;
  server_id?: string;
}

export interface CreateApplicationRequest {
  name: string;
  type: 'static-website' | 'web-service';
  framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
  domain?: string;
  git_repo?: string;
  build_command?: string;
  start_command?: string;
}

export interface FileStructureItem {
  name: string;
  type: 'file' | 'directory';
  size?: number;
  permissions: string;
  owner: string;
  modified: string;
  path: string;
}

// Server pool interface for load balancing
export interface SharedHostingServer {
  id: string;
  ip_address: string;
  hostname: string;
  region: string;
  status: 'active' | 'maintenance' | 'offline';
  max_users: number;
  current_users: number;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  load_average: number;
  last_sync: Date;
  created_at: Date;
}

// Payment status interface
export interface PaymentStatus {
  user_id: string;
  status: 'active' | 'grace_period' | 'suspended' | 'deleted';
  last_payment_date?: Date;
  grace_period_start?: Date;
  grace_period_end?: Date;
  grace_period_days: number; // 30 or 60 days
  deletion_scheduled?: Date;
}

class SharedHostingService {
  private readonly SHARED_SERVER_IP = process.env['SHARED_SERVER_IP'] || '*************'; // Your Vultr server
  private readonly BASE_PORT = 8000; // Starting port for user applications
  private readonly SSH_BASE_PORT = 2200; // Starting SSH port for users
  private readonly FTP_BASE_PORT = 2100; // Starting FTP port for users

  // Server pool for load balancing
  private serverPool: SharedHostingServer[] = [];

  // Payment tracking
  private paymentStatuses: Map<string, PaymentStatus> = new Map();

  constructor() {
    this.initializeServerPool();
    this.initializePaymentTracking();
  }

  // Initialize server pool based on environment configuration
  private async initializeServerPool(): Promise<void> {
    try {
      // Get server configuration from environment
      const serverConfig = process.env['SHARED_HOSTING_SERVERS'];

      if (serverConfig) {
        // Parse multiple servers from environment (JSON format)
        const servers = JSON.parse(serverConfig);
        this.serverPool = servers.map((server: any) => ({
          ...server,
          last_sync: new Date(),
          created_at: new Date()
        }));
      } else {
        // Default to single server setup
        this.serverPool = [{
          id: 'vultr-jnb-shared-01',
          ip_address: this.SHARED_SERVER_IP,
          hostname: 'achidas-shared-1',
          region: 'JNB',
          status: 'active' as const,
          max_users: 50,
          current_users: 0,
          cpu_usage: 0,
          memory_usage: 0,
          disk_usage: 0,
          load_average: 0,
          last_sync: new Date(),
          created_at: new Date()
        }];
      }

      logger.info(`Initialized server pool with ${this.serverPool.length} servers`);
    } catch (error) {
      logger.error('Failed to initialize server pool:', error);
      // Fallback to single server
      this.serverPool = [{
        id: 'vultr-jnb-shared-01',
        ip_address: this.SHARED_SERVER_IP,
        hostname: 'achidas-shared-1',
        region: 'JNB',
        status: 'active' as const,
        max_users: 50,
        current_users: 0,
        cpu_usage: 0,
        memory_usage: 0,
        disk_usage: 0,
        load_average: 0,
        last_sync: new Date(),
        created_at: new Date()
      }];
    }
  }

  // Initialize payment tracking system
  private async initializePaymentTracking(): Promise<void> {
    try {
      // In production, this would load from database
      // For now, initialize empty tracking
      logger.info('Payment tracking system initialized');
    } catch (error) {
      logger.error('Failed to initialize payment tracking:', error);
    }
  }

  // Get optimal server for new user allocation
  private async getOptimalServer(): Promise<SharedHostingServer> {
    // Update server metrics before selection
    await this.updateServerMetrics();

    // Filter active servers with capacity
    const availableServers = this.serverPool.filter(server =>
      server.status === 'active' &&
      server.current_users < server.max_users
    );

    if (availableServers.length === 0) {
      throw new Error('No available servers with capacity');
    }

    // Single server mode
    if (availableServers.length === 1) {
      return availableServers[0]!;
    }

    // Load balancing algorithm: prefer servers with lower utilization
    const sortedServers = availableServers.sort((a, b) => {
      const utilizationA = (a.current_users / a.max_users) + (a.cpu_usage / 100) + (a.memory_usage / 100);
      const utilizationB = (b.current_users / b.max_users) + (b.cpu_usage / 100) + (b.memory_usage / 100);
      return utilizationA - utilizationB;
    });

    return sortedServers[0]!;
  }

  // Update server metrics for all servers in pool
  private async updateServerMetrics(): Promise<void> {
    const updatePromises = this.serverPool.map(async (server) => {
      try {
        // Create SSH connection with server-specific config
        const ssh = new SSHConnection();
        // Override the default config for this specific server
        (ssh as any).config.host = server.ip_address;
        await ssh.connect();

        // Get current user count
        const userCountResult = await ssh.executeCommand("ls /home | grep '^user_' | wc -l");
        server.current_users = parseInt(userCountResult.stdout.trim()) || 0;

        // Get CPU usage
        const cpuResult = await ssh.executeCommand("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
        server.cpu_usage = parseFloat(cpuResult.stdout.trim()) || 0;

        // Get memory usage
        const memResult = await ssh.executeCommand("free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'");
        server.memory_usage = parseFloat(memResult.stdout.trim()) || 0;

        // Get disk usage
        const diskResult = await ssh.executeCommand("df / | tail -1 | awk '{print $5}' | cut -d'%' -f1");
        server.disk_usage = parseFloat(diskResult.stdout.trim()) || 0;

        // Get load average
        const loadResult = await ssh.executeCommand("uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1");
        server.load_average = parseFloat(loadResult.stdout.trim()) || 0;

        server.last_sync = new Date();
        await ssh.disconnect();
      } catch (error) {
        logger.error(`Failed to update metrics for server ${server.id}:`, error);
        server.status = 'offline';
      }
    });

    await Promise.allSettled(updatePromises);
  }

  // Initialize payment status for new user
  private async initializeUserPaymentStatus(userId: string): Promise<void> {
    const paymentStatus: PaymentStatus = {
      user_id: userId,
      status: 'active',
      last_payment_date: new Date(),
      grace_period_days: 30, // Default 30 days grace period
    };

    this.paymentStatuses.set(userId, paymentStatus);
    logger.info(`Initialized payment status for user: ${userId}`);
  }

  // Update user payment status
  async updateUserPaymentStatus(userId: string, status: 'active' | 'grace_period' | 'suspended' | 'deleted', gracePeriodDays?: number): Promise<void> {
    const paymentStatus = this.paymentStatuses.get(userId);

    if (!paymentStatus) {
      throw new Error(`Payment status not found for user: ${userId}`);
    }

    const now = new Date();

    switch (status) {
      case 'active':
        paymentStatus.status = 'active';
        paymentStatus.last_payment_date = now;
        delete paymentStatus.grace_period_start;
        delete paymentStatus.grace_period_end;
        delete paymentStatus.deletion_scheduled;
        break;

      case 'grace_period':
        paymentStatus.status = 'grace_period';
        paymentStatus.grace_period_start = now;
        paymentStatus.grace_period_days = gracePeriodDays || 30;

        const graceEnd = new Date(now);
        graceEnd.setDate(graceEnd.getDate() + paymentStatus.grace_period_days);
        paymentStatus.grace_period_end = graceEnd;

        const deletionDate = new Date(graceEnd);
        deletionDate.setDate(deletionDate.getDate() + 7); // 7 days after grace period
        paymentStatus.deletion_scheduled = deletionDate;
        break;

      case 'suspended':
        paymentStatus.status = 'suspended';
        break;

      case 'deleted':
        paymentStatus.status = 'deleted';
        break;
    }

    this.paymentStatuses.set(userId, paymentStatus);
    logger.info(`Updated payment status for user ${userId} to: ${status}`);
  }

  // Check and process payment-based user lifecycle
  async processPaymentBasedLifecycle(): Promise<void> {
    const now = new Date();

    for (const [userId, paymentStatus] of this.paymentStatuses.entries()) {
      try {
        switch (paymentStatus.status) {
          case 'active':
            // Check if payment is overdue (implement your payment check logic here)
            const daysSinceLastPayment = Math.floor((now.getTime() - (paymentStatus.last_payment_date?.getTime() || 0)) / (1000 * 60 * 60 * 24));

            if (daysSinceLastPayment > 35) { // 5 days past due
              await this.updateUserPaymentStatus(userId, 'grace_period');
              await this.suspendUser(userId, 'Payment overdue - entering grace period');
            }
            break;

          case 'grace_period':
            if (paymentStatus.grace_period_end && now > paymentStatus.grace_period_end) {
              await this.updateUserPaymentStatus(userId, 'suspended');
              logger.warn(`User ${userId} grace period expired, scheduling for deletion`);
            }
            break;

          case 'suspended':
            if (paymentStatus.deletion_scheduled && now > paymentStatus.deletion_scheduled) {
              await this.deleteUser(userId);
              await this.updateUserPaymentStatus(userId, 'deleted');
              logger.info(`User ${userId} deleted due to non-payment`);
            }
            break;
        }
      } catch (error) {
        logger.error(`Failed to process payment lifecycle for user ${userId}:`, error);
      }
    }
  }

  // Get payment status for user
  getUserPaymentStatus(userId: string): PaymentStatus | undefined {
    return this.paymentStatuses.get(userId);
  }

  // Get server pool status
  getServerPoolStatus(): SharedHostingServer[] {
    return [...this.serverPool];
  }

  // Add new server to pool
  async addServerToPool(serverConfig: Omit<SharedHostingServer, 'current_users' | 'cpu_usage' | 'memory_usage' | 'disk_usage' | 'load_average' | 'last_sync' | 'created_at'>): Promise<void> {
    const newServer: SharedHostingServer = {
      ...serverConfig,
      current_users: 0,
      cpu_usage: 0,
      memory_usage: 0,
      disk_usage: 0,
      load_average: 0,
      last_sync: new Date(),
      created_at: new Date()
    };

    this.serverPool.push(newServer);
    logger.info(`Added new server to pool: ${newServer.id}`);
  }

  // Remove server from pool
  async removeServerFromPool(serverId: string): Promise<void> {
    const serverIndex = this.serverPool.findIndex(server => server.id === serverId);

    if (serverIndex === -1) {
      throw new Error(`Server not found: ${serverId}`);
    }

    const server = this.serverPool[serverIndex]!;

    if (server.current_users > 0) {
      throw new Error(`Cannot remove server ${serverId} - still has ${server.current_users} users`);
    }

    this.serverPool.splice(serverIndex, 1);
    logger.info(`Removed server from pool: ${serverId}`);
  }

  // Synchronize files between servers using rsync
  async synchronizeServers(): Promise<void> {
    if (this.serverPool.length <= 1) {
      logger.info('Single server mode - no synchronization needed');
      return;
    }

    const activeServers = this.serverPool.filter(server => server.status === 'active');

    if (activeServers.length <= 1) {
      logger.info('Only one active server - no synchronization needed');
      return;
    }

    // Use first server as primary source
    const primaryServer = activeServers[0]!;
    const secondaryServers = activeServers.slice(1);

    logger.info(`Starting synchronization from primary server: ${primaryServer.id}`);

    const syncPromises = secondaryServers.map(async (targetServer) => {
      try {
        await this.syncServerToServer(primaryServer, targetServer);
        logger.info(`Synchronized ${primaryServer.id} -> ${targetServer.id}`);
      } catch (error) {
        logger.error(`Failed to sync ${primaryServer.id} -> ${targetServer.id}:`, error);
      }
    });

    await Promise.allSettled(syncPromises);
    logger.info('Server synchronization completed');
  }

  // Sync files from source server to target server
  private async syncServerToServer(sourceServer: SharedHostingServer, targetServer: SharedHostingServer): Promise<void> {
    const ssh = new SSHConnection();
    try {
      // Connect to target server
      (ssh as any).config.host = targetServer.ip_address;
      await ssh.connect();

      // Rsync command to sync from source to target
      // Sync user home directories and web files
      const rsyncCommand = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/var/www/ /var/www/`;

      await ssh.executeCommand(rsyncCommand);

      // Also sync user accounts (passwd, shadow, group files)
      const userSyncCommands = [
        `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/passwd /etc/passwd.new`,
        `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/shadow /etc/shadow.new`,
        `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/group /etc/group.new`,
        // Backup current files and replace
        `cp /etc/passwd /etc/passwd.backup`,
        `cp /etc/shadow /etc/shadow.backup`,
        `cp /etc/group /etc/group.backup`,
        `mv /etc/passwd.new /etc/passwd`,
        `mv /etc/shadow.new /etc/shadow`,
        `mv /etc/group.new /etc/group`
      ];

      for (const command of userSyncCommands) {
        await ssh.executeCommand(command);
      }

      // Update target server's last sync time
      targetServer.last_sync = new Date();

    } catch (error) {
      logger.error(`Rsync failed between ${sourceServer.id} and ${targetServer.id}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Start automatic synchronization (every 5 minutes)
  startAutoSync(): void {
    // Clear any existing interval
    if ((this as any).syncInterval) {
      clearInterval((this as any).syncInterval);
    }

    // Set up 5-minute sync interval
    (this as any).syncInterval = setInterval(async () => {
      try {
        await this.synchronizeServers();
      } catch (error) {
        logger.error('Auto-sync failed:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    logger.info('Auto-sync started (5-minute intervals)');
  }

  // Stop automatic synchronization
  stopAutoSync(): void {
    if ((this as any).syncInterval) {
      clearInterval((this as any).syncInterval);
      (this as any).syncInterval = null;
      logger.info('Auto-sync stopped');
    }
  }

  // Start payment lifecycle monitoring (daily check)
  startPaymentLifecycleMonitoring(): void {
    // Clear any existing interval
    if ((this as any).paymentInterval) {
      clearInterval((this as any).paymentInterval);
    }

    // Set up daily payment check
    (this as any).paymentInterval = setInterval(async () => {
      try {
        await this.processPaymentBasedLifecycle();
      } catch (error) {
        logger.error('Payment lifecycle processing failed:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours

    logger.info('Payment lifecycle monitoring started (daily checks)');
  }

  // Stop payment lifecycle monitoring
  stopPaymentLifecycleMonitoring(): void {
    if ((this as any).paymentInterval) {
      clearInterval((this as any).paymentInterval);
      (this as any).paymentInterval = null;
      logger.info('Payment lifecycle monitoring stopped');
    }
  }

  // Create a new shared hosting user with Linux isolation
  async createUser(userData: CreateSharedUserRequest): Promise<SharedHostingUser> {
    try {
      // Get optimal server for user allocation
      const selectedServer = await this.getOptimalServer();

      const linuxUsername = `user_${userData.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
      const homeDirectory = `/var/www/${linuxUsername}`;
      const port = await this.getNextAvailablePort();
      const sshPort = await this.getNextAvailableSSHPort();
      const ftpPort = await this.getNextAvailableFTPPort();

      // Create Linux user with isolated home directory on selected server
      await this.createLinuxUser(linuxUsername, homeDirectory, selectedServer);

      // Set up resource limits on selected server
      await this.setupResourceLimits(linuxUsername, userData.plan, selectedServer);

      // Create user directory structure on selected server
      await this.createUserDirectoryStructure(homeDirectory, linuxUsername, selectedServer);

      // Set up bandwidth throttling on selected server
      await this.setupBandwidthThrottling(linuxUsername, port, selectedServer);

      // Initialize payment status for new user
      await this.initializeUserPaymentStatus(userData.user_id);

      const sharedUser: SharedHostingUser = {
        id: `shared_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        user_id: userData.user_id,
        username: userData.username,
        linux_username: linuxUsername,
        home_directory: homeDirectory,
        plan: userData.plan,
        status: 'active',
        server_id: selectedServer.id,
        server_ip: selectedServer.ip_address,
        port,
        ssh_port: sshPort,
        ftp_port: ftpPort,
        resource_limits: this.getPlanLimits(userData.plan),
        usage: {
          cpu_usage: 0,
          memory_usage: 0,
          bandwidth_used: 0,
          storage_used: 0
        },
        created_at: new Date().toISOString(),
        applications: []
      };

      // Update server user count
      selectedServer.current_users++;

      logger.info(`Created shared hosting user: ${linuxUsername} at ${homeDirectory} on server: ${selectedServer.id}`);
      return sharedUser;
    } catch (error) {
      logger.error('Failed to create shared hosting user:', error);
      throw new Error('Failed to create shared hosting user');
    }
  }

  // Create Linux user with proper isolation using SSH
  private async createLinuxUser(username: string, homeDirectory: string, server?: SharedHostingServer): Promise<void> {
    const ssh = new SSHConnection();
    try {
      // Override SSH config for specific server if provided
      if (server) {
        (ssh as any).config.host = server.ip_address;
      }
      await ssh.connect();

      // Create user with custom home directory
      await ssh.executeCommand(`adduser --disabled-password --gecos "" --home ${homeDirectory} ${username}`);

      // Set strict permissions (only user can access their directory)
      await ssh.executeCommand(`chmod 700 ${homeDirectory}`);
      await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}`);

      // Disable cron access to prevent background processes
      await ssh.executeCommand(`echo "${username}" >> /etc/cron.deny`);

      logger.info(`Created Linux user: ${username} with home: ${homeDirectory}`);
    } catch (error) {
      logger.error(`Failed to create Linux user ${username}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Set up systemd resource limits for CPU and memory using SSH
  private async setupResourceLimits(username: string, plan: string, server?: SharedHostingServer): Promise<void> {
    const ssh = new SSHConnection();
    try {
      // Override SSH config for specific server if provided
      if (server) {
        (ssh as any).config.host = server.ip_address;
      }
      await ssh.connect();
      const limits = this.getPlanLimits(plan);

      // Create systemd slice for user
      const sliceConfig = `[Slice]
CPUQuota=${limits.cpu_quota}%
MemoryMax=${limits.memory_max}M
TasksMax=50`;

      await ssh.executeCommand(`mkdir -p /etc/systemd/system/${username}.slice.d`);
      await ssh.executeCommand(`echo '${sliceConfig}' > /etc/systemd/system/${username}.slice.d/limits.conf`);

      // Reload systemd and apply limits
      await ssh.executeCommand('systemctl daemon-reexec');
      await ssh.executeCommand(`systemctl restart user-$(id -u ${username}).slice || true`);

      logger.info(`Set up resource limits for user: ${username}, plan: ${plan}`);
    } catch (error) {
      logger.error(`Failed to setup resource limits for ${username}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Create user directory structure for web hosting using SSH
  private async createUserDirectoryStructure(homeDirectory: string, username: string, server?: SharedHostingServer): Promise<void> {
    const ssh = new SSHConnection();
    try {
      // Override SSH config for specific server if provided
      if (server) {
        (ssh as any).config.host = server.ip_address;
      }
      await ssh.connect();
      const directories = [
        'public_html',      // Web root
        'logs',            // Application logs
        'tmp',             // Temporary files
        'backups',         // User backups
        'ssl',             // SSL certificates
        'apps',            // Application directories
        'apps/static',     // Static websites
        'apps/web-service', // Web services
        'apps/nodejs',     // Node.js applications
        'apps/php'         // PHP applications
      ];

      for (const dir of directories) {
        const fullPath = `${homeDirectory}/${dir}`;
        await ssh.executeCommand(`mkdir -p ${fullPath}`);
        await ssh.executeCommand(`chown ${username}:${username} ${fullPath}`);
        await ssh.executeCommand(`chmod 755 ${fullPath}`);
      }

      // Create default index.html
      const defaultHtml = `<!DOCTYPE html>
<html>
<head>
    <title>Welcome to ${username}'s Website</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { color: #007bff; font-size: 2em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Achidas Hosting</div>
        <h1>Welcome to your new website!</h1>
        <p>Your shared hosting account is ready. Upload your files to the <code>public_html</code> directory.</p>
        <p>Server: ${this.SHARED_SERVER_IP}</p>
        <p>Username: ${username}</p>
    </div>
</body>
</html>`;

      // Write the HTML file using SSH
      const escapedHtml = defaultHtml.replace(/'/g, "'\"'\"'");
      await ssh.executeCommand(`echo '${escapedHtml}' > ${homeDirectory}/public_html/index.html`);
      await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}/public_html/index.html`);

      logger.info(`Created directory structure for user: ${username}`);
    } catch (error) {
      logger.error(`Failed to create directory structure for ${username}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Set up bandwidth throttling using tc (traffic control)
  private async setupBandwidthThrottling(username: string, port: number, server?: SharedHostingServer): Promise<void> {
    try {
      if (server) {
        // Use SSH for remote server bandwidth throttling
        const ssh = new SSHConnection();
        (ssh as any).config.host = server.ip_address;
        await ssh.connect();

        // Set up traffic control for user's port on remote server
        await ssh.executeCommand(`tc qdisc add dev eth0 root handle 1: htb default 30 || true`);
        await ssh.executeCommand(`tc class add dev eth0 parent 1: classid 1:1 htb rate 100mbit || true`);
        await ssh.executeCommand(`tc class add dev eth0 parent 1:1 classid 1:${port} htb rate 5mbit ceil 10mbit || true`);

        await ssh.disconnect();
      } else {
        // Local server bandwidth throttling
        await execAsync(`sudo tc qdisc add dev eth0 root handle 1: htb default 30`);
        await execAsync(`sudo tc class add dev eth0 parent 1: classid 1:1 htb rate 100mbit`);
        await execAsync(`sudo tc class add dev eth0 parent 1:1 classid 1:${port} htb rate 5mbit ceil 10mbit`);
        await execAsync(`sudo tc filter add dev eth0 protocol ip parent 1:0 prio 1 u32 match ip dport ${port} 0xffff flowid 1:${port}`);
      }

      logger.info(`Set up bandwidth throttling for user: ${username} on port: ${port}`);
    } catch (error) {
      logger.error(`Failed to setup bandwidth throttling for ${username}:`, error);
      // Don't throw error as this is not critical for basic functionality
    }
  }

  // Get plan resource limits
  private getPlanLimits(plan: string): SharedHostingUser['resource_limits'] {
    const planLimits: Record<string, SharedHostingUser['resource_limits']> = {
      'free': {
        cpu_quota: 2,
        memory_max: 128,
        bandwidth_limit: 5,
        storage_limit: 1
      },
      'starter': {
        cpu_quota: 5,
        memory_max: 256,
        bandwidth_limit: 25,
        storage_limit: 5
      },
      'basic': {
        cpu_quota: 10,
        memory_max: 512,
        bandwidth_limit: 50,
        storage_limit: 10
      },
      'standard': {
        cpu_quota: 15,
        memory_max: 1024,
        bandwidth_limit: 100,
        storage_limit: 20
      },
      'pro': {
        cpu_quota: 25,
        memory_max: 2048,
        bandwidth_limit: 250,
        storage_limit: 50
      }
    };

    return planLimits[plan] ?? planLimits['starter']!;
  }

  // Get next available port for user applications
  private async getNextAvailablePort(): Promise<number> {
    // In production, this should check a database or port registry
    return this.BASE_PORT + Math.floor(Math.random() * 1000);
  }

  private async getNextAvailableSSHPort(): Promise<number> {
    return this.SSH_BASE_PORT + Math.floor(Math.random() * 100);
  }

  private async getNextAvailableFTPPort(): Promise<number> {
    return this.FTP_BASE_PORT + Math.floor(Math.random() * 100);
  }

  // Create application for shared user
  async createApplication(userId: string, appData: CreateApplicationRequest): Promise<SharedHostingApplication> {
    try {
      const appId = `app_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const subdomain = `${appData.name.toLowerCase().replace(/[^a-z0-9]/g, '')}-${userId.substring(userId.length - 6)}`;
      const directory = `/var/www/user_${userId}/apps/${appData.type}/${appData.name}`;

      // Create application directory
      await execAsync(`sudo mkdir -p ${directory}`);
      await execAsync(`sudo chown user_${userId}:user_${userId} ${directory}`);

      // Set up application based on type
      await this.setupApplicationEnvironment(directory, appData.type, appData);

      const application: SharedHostingApplication = {
        id: appId,
        name: appData.name,
        type: appData.type,
        ...(appData.framework && { framework: appData.framework }),
        ...(appData.domain && { domain: appData.domain }),
        subdomain: `${subdomain}.achidas.com`,
        directory,
        status: 'stopped',
        ssl_enabled: false,
        created_at: new Date().toISOString()
      };

      logger.info(`Created application: ${appData.name} for user: ${userId}`);
      return application;
    } catch (error) {
      logger.error('Failed to create application:', error);
      throw new Error('Failed to create application');
    }
  }

  // Set up application environment based on type and framework
  private async setupApplicationEnvironment(directory: string, type: string, appData: CreateApplicationRequest): Promise<void> {
    try {
      if (type === 'static-website') {
        await this.setupStaticWebsite(directory, appData.framework);
      } else if (type === 'web-service') {
        await this.setupWebService(directory, appData);
      }
    } catch (error) {
      logger.error(`Failed to setup ${type} environment:`, error);
      throw error;
    }
  }

  // Set up static website environment
  private async setupStaticWebsite(directory: string, framework?: string): Promise<void> {
    const frameworkInfo = this.getFrameworkInfo(framework);

    const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>${frameworkInfo.title}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #007bff; font-size: 2.5em; margin-bottom: 10px; }
        .framework-badge { background: ${frameworkInfo.color}; color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9em; }
        .instructions { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">${frameworkInfo.icon}</div>
            <h1>${frameworkInfo.title}</h1>
            <span class="framework-badge">${framework || 'HTML'}</span>
            <p>${frameworkInfo.description}</p>
        </div>
        <div class="instructions">
            <h2>Getting Started</h2>
            ${frameworkInfo.instructions}
        </div>
        <div class="content">
            <h3>File Structure</h3>
            <p>Your files should be organized as follows:</p>
            <ul>
                ${frameworkInfo.structure.map((item: any) => `<li><strong>${item.name}</strong> - ${item.description}</li>`).join('')}
            </ul>
        </div>
    </div>
</body>
</html>`;

    await fs.writeFile(path.join(directory, 'index.html'), indexHtml);

    // Create framework-specific directories
    const directories = ['css', 'js', 'images', 'assets'];
    if (framework === 'react' || framework === 'vue' || framework === 'angular') {
      directories.push('dist', 'build', 'public');
    }

    await execAsync(`sudo mkdir -p ${directory}/{${directories.join(',')}}`);

    // Create framework-specific files
    if (framework === 'react') {
      await this.createReactFiles(directory);
    } else if (framework === 'vue') {
      await this.createVueFiles(directory);
    } else if (framework === 'angular') {
      await this.createAngularFiles(directory);
    }
  }

  // Get framework-specific information
  private getFrameworkInfo(framework?: string) {
    const frameworks: Record<string, any> = {
      html: {
        title: 'Static HTML Website',
        icon: '🌐',
        color: '#e34c26',
        description: 'Upload your HTML, CSS, and JavaScript files here.',
        instructions: `
          <ol>
            <li>Replace this index.html with your own</li>
            <li>Upload your CSS files to the 'css' folder</li>
            <li>Upload your JavaScript files to the 'js' folder</li>
            <li>Upload images to the 'images' folder</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'css/', description: 'Stylesheets' },
          { name: 'js/', description: 'JavaScript files' },
          { name: 'images/', description: 'Images and media' }
        ]
      },
      react: {
        title: 'React Application',
        icon: '⚛️',
        color: '#61dafb',
        description: 'Upload your React build files (npm run build output).',
        instructions: `
          <ol>
            <li>Build your React app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'build' or 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Static assets should be in the 'static' folder</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'static/', description: 'Built CSS, JS, and media files' },
          { name: 'build/', description: 'React build output' },
          { name: 'public/', description: 'Public assets' }
        ]
      },
      vue: {
        title: 'Vue.js Application',
        icon: '🖖',
        color: '#4fc08d',
        description: 'Upload your Vue.js build files (npm run build output).',
        instructions: `
          <ol>
            <li>Build your Vue app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Assets should be in the appropriate folders</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'dist/', description: 'Vue build output' },
          { name: 'assets/', description: 'Built assets' },
          { name: 'css/', description: 'Stylesheets' }
        ]
      },
      angular: {
        title: 'Angular Application',
        icon: '🅰️',
        color: '#dd0031',
        description: 'Upload your Angular build files (ng build output).',
        instructions: `
          <ol>
            <li>Build your Angular app: <code>ng build --prod</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Configure routing for SPA if needed</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'dist/', description: 'Angular build output' },
          { name: 'assets/', description: 'Static assets' },
          { name: 'styles/', description: 'Global styles' }
        ]
      }
    };

    return frameworks[framework || 'html'] || frameworks['html'];
  }

  // Create React-specific files
  private async createReactFiles(directory: string): Promise<void> {
    const packageJson = {
      name: "react-static-site",
      version: "1.0.0",
      description: "React static site on Achidas hosting",
      scripts: {
        build: "react-scripts build",
        start: "serve -s build"
      }
    };

    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  // Create Vue-specific files
  private async createVueFiles(directory: string): Promise<void> {
    const packageJson = {
      name: "vue-static-site",
      version: "1.0.0",
      description: "Vue.js static site on Achidas hosting",
      scripts: {
        build: "vue-cli-service build",
        start: "serve -s dist"
      }
    };

    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  // Create Angular-specific files
  private async createAngularFiles(directory: string): Promise<void> {
    const packageJson = {
      name: "angular-static-site",
      version: "1.0.0",
      description: "Angular static site on Achidas hosting",
      scripts: {
        build: "ng build --prod",
        start: "serve -s dist"
      }
    };

    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  // Set up web service environment based on framework
  private async setupWebService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const framework = appData.framework || 'nodejs';

    // Install runtime/packages for the framework
    await this.installFrameworkRuntime(framework);

    switch (framework) {
      case 'nodejs':
        await this.setupNodeJSService(directory, appData);
        break;
      case 'python':
        await this.setupPythonService(directory, appData);
        break;
      case 'rust':
        await this.setupRustService(directory, appData);
        break;
      case 'php':
        await this.setupPHPService(directory, appData);
        break;
      case 'go':
        await this.setupGoService(directory, appData);
        break;
      case 'java':
        await this.setupJavaService(directory, appData);
        break;
      case 'dotnet':
        await this.setupDotNetService(directory, appData);
        break;
      case 'ruby':
        await this.setupRubyService(directory, appData);
        break;
      default:
        await this.setupNodeJSService(directory, appData);
    }
  }

  // Install runtime and packages for programming languages dynamically
  private async installFrameworkRuntime(framework: string): Promise<void> {
    try {
      logger.info(`Installing runtime for framework: ${framework}`);

      switch (framework) {
        case 'nodejs':
          await this.installNodeJS();
          break;
        case 'python':
          await this.installPython();
          break;
        case 'rust':
          await this.installRust();
          break;
        case 'php':
          await this.installPHP();
          break;
        case 'go':
          await this.installGo();
          break;
        case 'java':
          await this.installJava();
          break;
        case 'dotnet':
          await this.installDotNet();
          break;
        case 'ruby':
          await this.installRuby();
          break;
        default:
          logger.warn(`Unknown framework: ${framework}, skipping runtime installation`);
      }
    } catch (error) {
      logger.error(`Failed to install runtime for ${framework}:`, error);
      throw error;
    }
  }

  // Install Node.js runtime
  private async installNodeJS(): Promise<void> {
    try {
      // Check if Node.js is already installed
      const nodeCheck = await execAsync('node --version').catch(() => null);
      if (nodeCheck) {
        logger.info('Node.js already installed:', nodeCheck.stdout.trim());
        return;
      }

      logger.info('Installing Node.js...');
      await execAsync('curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -');
      await execAsync('sudo apt-get install -y nodejs');

      // Install global packages
      await execAsync('sudo npm install -g pm2 serve nodemon');

      logger.info('Node.js installation completed');
    } catch (error) {
      logger.error('Failed to install Node.js:', error);
      throw error;
    }
  }

  // Install Python runtime
  private async installPython(): Promise<void> {
    try {
      // Check if Python 3 is already installed
      const pythonCheck = await execAsync('python3 --version').catch(() => null);
      if (pythonCheck) {
        logger.info('Python already installed:', pythonCheck.stdout.trim());
        return;
      }

      logger.info('Installing Python...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y python3 python3-pip python3-venv python3-dev');

      // Install common packages
      await execAsync('sudo pip3 install fastapi uvicorn gunicorn flask django');

      logger.info('Python installation completed');
    } catch (error) {
      logger.error('Failed to install Python:', error);
      throw error;
    }
  }

  // Install Rust runtime
  private async installRust(): Promise<void> {
    try {
      // Check if Rust is already installed
      const rustCheck = await execAsync('rustc --version').catch(() => null);
      if (rustCheck) {
        logger.info('Rust already installed:', rustCheck.stdout.trim());
        return;
      }

      logger.info('Installing Rust...');
      await execAsync('curl --proto "=https" --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y');
      await execAsync('source ~/.cargo/env');

      // Install common tools
      await execAsync('~/.cargo/bin/cargo install cargo-watch');

      logger.info('Rust installation completed');
    } catch (error) {
      logger.error('Failed to install Rust:', error);
      throw error;
    }
  }

  // Install PHP runtime
  private async installPHP(): Promise<void> {
    try {
      // Check if PHP is already installed
      const phpCheck = await execAsync('php --version').catch(() => null);
      if (phpCheck) {
        logger.info('PHP already installed:', phpCheck.stdout.trim());
        return;
      }

      logger.info('Installing PHP...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y php php-cli php-fpm php-json php-common php-mysql php-zip php-gd php-mbstring php-curl php-xml php-pear php-bcmath');

      // Install Composer
      await execAsync('curl -sS https://getcomposer.org/installer | php');
      await execAsync('sudo mv composer.phar /usr/local/bin/composer');

      logger.info('PHP installation completed');
    } catch (error) {
      logger.error('Failed to install PHP:', error);
      throw error;
    }
  }

  // Install Go runtime
  private async installGo(): Promise<void> {
    try {
      // Check if Go is already installed
      const goCheck = await execAsync('go version').catch(() => null);
      if (goCheck) {
        logger.info('Go already installed:', goCheck.stdout.trim());
        return;
      }

      logger.info('Installing Go...');
      await execAsync('wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz');
      await execAsync('sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz');
      await execAsync('echo "export PATH=$PATH:/usr/local/go/bin" | sudo tee -a /etc/profile');
      await execAsync('rm go1.21.5.linux-amd64.tar.gz');

      logger.info('Go installation completed');
    } catch (error) {
      logger.error('Failed to install Go:', error);
      throw error;
    }
  }

  // Install Java runtime
  private async installJava(): Promise<void> {
    try {
      // Check if Java is already installed
      const javaCheck = await execAsync('java --version').catch(() => null);
      if (javaCheck) {
        logger.info('Java already installed:', javaCheck.stdout.trim());
        return;
      }

      logger.info('Installing Java...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y openjdk-17-jdk maven gradle');

      logger.info('Java installation completed');
    } catch (error) {
      logger.error('Failed to install Java:', error);
      throw error;
    }
  }

  // Install .NET runtime
  private async installDotNet(): Promise<void> {
    try {
      // Check if .NET is already installed
      const dotnetCheck = await execAsync('dotnet --version').catch(() => null);
      if (dotnetCheck) {
        logger.info('.NET already installed:', dotnetCheck.stdout.trim());
        return;
      }

      logger.info('Installing .NET...');
      await execAsync('wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb');
      await execAsync('sudo dpkg -i packages-microsoft-prod.deb');
      await execAsync('rm packages-microsoft-prod.deb');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y dotnet-sdk-8.0');

      logger.info('.NET installation completed');
    } catch (error) {
      logger.error('Failed to install .NET:', error);
      throw error;
    }
  }

  // Install Ruby runtime
  private async installRuby(): Promise<void> {
    try {
      // Check if Ruby is already installed
      const rubyCheck = await execAsync('ruby --version').catch(() => null);
      if (rubyCheck) {
        logger.info('Ruby already installed:', rubyCheck.stdout.trim());
        return;
      }

      logger.info('Installing Ruby...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y ruby-full build-essential zlib1g-dev');

      // Install bundler and rails
      await execAsync('sudo gem install bundler rails');

      logger.info('Ruby installation completed');
    } catch (error) {
      logger.error('Failed to install Ruby:', error);
      throw error;
    }
  }

  // Set up Node.js web service
  private async setupNodeJSService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const packageJsonPath = path.join(directory, 'package.json');
    const hasUserPackageJson = require('fs').existsSync(packageJsonPath);

    // Only create package.json if user hasn't provided one
    if (!hasUserPackageJson) {
      const packageJson = {
        name: appData.name,
        version: "1.0.0",
        description: "Node.js web service on Achidas shared hosting",
        main: "index.js",
        scripts: {
          start: appData.start_command || "node index.js",
          build: appData.build_command || "npm install"
        }
      };

      await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));

      // Create basic index.js only if user hasn't provided their own
      const indexPath = path.join(directory, 'index.js');
      const hasUserIndex = require('fs').existsSync(indexPath);

      if (!hasUserIndex) {
        const indexJs = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from ${appData.name}!' });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: '${appData.name}'
  });
});

app.listen(port, () => {
  console.log(\`🚀 ${appData.name} running on port \${port}\`);
});
`;
        await fs.writeFile(indexPath, indexJs);
      }
    }

    // Install dependencies from user's package.json (or the one we created)
    try {
      await execAsync(`cd ${directory} && npm install`);
      logger.info(`Installed Node.js dependencies from package.json for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install Node.js dependencies for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, 'node_modules/\n.env\n*.log\n');
    }
  }

  // Set up Python web service
  private async setupPythonService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const requirementsPath = path.join(directory, 'requirements.txt');
    const hasUserRequirements = require('fs').existsSync(requirementsPath);

    // Only create requirements.txt if user hasn't provided one
    if (!hasUserRequirements) {
      const requirementsTxt = `fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0`;

      await fs.writeFile(requirementsPath, requirementsTxt);

      // Create basic main.py only if user hasn't provided their own
      const mainPath = path.join(directory, 'main.py');
      const hasUserMain = require('fs').existsSync(mainPath);

      if (!hasUserMain) {
        const mainPy = `
from fastapi import FastAPI
import uvicorn
from datetime import datetime

app = FastAPI(title="${appData.name}", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "Hello from ${appData.name}!"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "${appData.name}"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
`;
        await fs.writeFile(mainPath, mainPy.trim());
      }
    }

    // Install dependencies from user's requirements.txt (or the one we created)
    try {
      await execAsync(`cd ${directory} && python3 -m pip install -r requirements.txt`);
      logger.info(`Installed Python dependencies from requirements.txt for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install Python dependencies for ${appData.name}:`, error);
    }

    // Create start script
    const startSh = `#!/bin/bash
${appData.start_command || 'uvicorn main:app --host 0.0.0.0 --port 8000'}
`;
    await fs.writeFile(path.join(directory, 'start.sh'), startSh.trim());
    await execAsync(`chmod +x ${path.join(directory, 'start.sh')}`);

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '__pycache__/\n*.pyc\n.env\n*.log\nvenv/\n');
    }
  }

  // Set up Rust web service
  private async setupRustService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const cargoTomlPath = path.join(directory, 'Cargo.toml');
    const hasUserCargo = require('fs').existsSync(cargoTomlPath);

    // Only create Cargo.toml if user hasn't provided one
    if (!hasUserCargo) {
      const cargoToml = `[package]
name = "${appData.name.replace(/[^a-z0-9_]/g, '_')}"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.4"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }`;

      await fs.writeFile(cargoTomlPath, cargoToml);

      // Create src directory and main.rs only if user hasn't provided their own
      await execAsync(`mkdir -p ${directory}/src`);
      const mainRsPath = path.join(directory, 'src', 'main.rs');
      const hasUserMain = require('fs').existsSync(mainRsPath);

      if (!hasUserMain) {
        const mainRs = `
use actix_web::{web, App, HttpResponse, HttpServer, Result};
use serde::Serialize;

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    service: String,
}

async fn health() -> Result<HttpResponse> {
    let response = HealthResponse {
        status: "healthy".to_string(),
        service: "${appData.name}".to_string(),
    };
    Ok(HttpResponse::Ok().json(response))
}

async fn hello() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": format!("Hello from {}!", "${appData.name}")
    })))
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    println!("🚀 ${appData.name} starting on port 8080");

    HttpServer::new(|| {
        App::new()
            .route("/", web::get().to(hello))
            .route("/health", web::get().to(health))
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
`;
        await fs.writeFile(mainRsPath, mainRs.trim());
      }
    }

    // Build the Rust project using user's Cargo.toml (or the one we created)
    try {
      await execAsync(`cd ${directory} && cargo build --release`);
      logger.info(`Built Rust project from Cargo.toml for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build Rust project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/target\nCargo.lock\n.env\n*.log\n');
    }
  }

  // Set up PHP web service
  private async setupPHPService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const composerJsonPath = path.join(directory, 'composer.json');
    const hasUserComposer = require('fs').existsSync(composerJsonPath);

    // Only create composer.json if user hasn't provided one
    if (!hasUserComposer) {
      const composerJson = {
        name: `achidas/${appData.name}`,
        description: `${appData.name} PHP web service`,
        type: "project",
        require: {
          "php": ">=8.0"
        },
        autoload: {
          "psr-4": {
            "App\\": "src/"
          }
        }
      };

      await fs.writeFile(composerJsonPath, JSON.stringify(composerJson, null, 2));

      // Create basic index.php only if user hasn't provided their own
      const indexPath = path.join(directory, 'index.php');
      const hasUserIndex = require('fs').existsSync(indexPath);

      if (!hasUserIndex) {
        const indexPhp = `
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$request_uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

switch ($request_uri) {
    case '/':
        echo json_encode(['message' => 'Hello from ${appData.name}!']);
        break;
    case '/health':
        echo json_encode([
            'status' => 'healthy',
            'timestamp' => date('c'),
            'service' => '${appData.name}'
        ]);
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found']);
}
?>`;
        await fs.writeFile(indexPath, indexPhp.trim());
      }
    }

    // Install dependencies from user's composer.json (or the one we created)
    try {
      await execAsync(`cd ${directory} && composer install --no-dev`);
      logger.info(`Installed PHP dependencies from composer.json for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install PHP dependencies for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/vendor\n.env\n*.log\n');
    }
  }

  // Set up Go web service
  private async setupGoService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const goModPath = path.join(directory, 'go.mod');
    const hasUserGoMod = require('fs').existsSync(goModPath);

    // Only create go.mod if user hasn't provided one
    if (!hasUserGoMod) {
      const goMod = `module ${appData.name}

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
)`;

      await fs.writeFile(goModPath, goMod);

      // Create basic main.go only if user hasn't provided their own
      const mainGoPath = path.join(directory, 'main.go');
      const hasUserMain = require('fs').existsSync(mainGoPath);

      if (!hasUserMain) {
        const mainGo = `
package main

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

func main() {
    r := gin.Default()

    r.GET("/", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "message": "Hello from ${appData.name}!",
        })
    })

    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "status":  "healthy",
            "service": "${appData.name}",
        })
    })

    r.Run(":8080")
}
`;
        await fs.writeFile(mainGoPath, mainGo.trim());
      }
    }

    // Install dependencies from user's go.mod (or the one we created)
    try {
      await execAsync(`cd ${directory} && go mod tidy`);
      await execAsync(`cd ${directory} && go build`);
      logger.info(`Built Go project from go.mod for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build Go project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '*.exe\n*.log\n.env\n');
    }
  }

  // Set up Java web service
  private async setupJavaService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const pomXmlPath = path.join(directory, 'pom.xml');
    const hasUserPom = require('fs').existsSync(pomXmlPath);

    // Only create pom.xml if user hasn't provided one
    if (!hasUserPom) {
      const pomXml = `<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.achidas</groupId>
    <artifactId>${appData.name}</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>3.1.0</version>
        </dependency>
    </dependencies>
</project>`;

      await fs.writeFile(pomXmlPath, pomXml);

      // Create basic Java application only if user hasn't provided their own
      await execAsync(`mkdir -p ${directory}/src/main/java/com/achidas`);
      const mainJavaPath = path.join(directory, 'src/main/java/com/achidas/Application.java');
      const hasUserMain = require('fs').existsSync(mainJavaPath);

      if (!hasUserMain) {
        const mainJava = `package com.achidas;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
public class Application {

    @GetMapping("/")
    public String hello() {
        return "Hello from ${appData.name}!";
    }

    @GetMapping("/health")
    public String health() {
        return "{\\"status\\": \\"healthy\\", \\"service\\": \\"${appData.name}\\"}";
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}`;
        await fs.writeFile(mainJavaPath, mainJava);
      }
    }

    // Build with Maven using user's pom.xml (or the one we created)
    try {
      await execAsync(`cd ${directory} && mvn clean compile`);
      logger.info(`Built Java project from pom.xml for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build Java project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/target\n*.class\n.env\n*.log\n');
    }
  }

  // Set up .NET web service
  private async setupDotNetService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const csprojPath = path.join(directory, `${appData.name}.csproj`);
    const hasUserCsproj = require('fs').existsSync(csprojPath);

    // Only create .csproj if user hasn't provided one
    if (!hasUserCsproj) {
      const csproj = `<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
</Project>`;

      await fs.writeFile(csprojPath, csproj);

      // Create basic Program.cs only if user hasn't provided their own
      const programPath = path.join(directory, 'Program.cs');
      const hasUserProgram = require('fs').existsSync(programPath);

      if (!hasUserProgram) {
        const programCs = `var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => "Hello from ${appData.name}!");
app.MapGet("/health", () => new { status = "healthy", service = "${appData.name}" });

app.Run();`;
        await fs.writeFile(programPath, programCs);
      }
    }

    // Build with dotnet using user's .csproj (or the one we created)
    try {
      await execAsync(`cd ${directory} && dotnet build`);
      logger.info(`Built .NET project from .csproj for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build .NET project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/bin\n/obj\n.env\n*.log\n');
    }
  }

  // Set up Ruby web service
  private async setupRubyService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const gemfilePath = path.join(directory, 'Gemfile');
    const hasUserGemfile = require('fs').existsSync(gemfilePath);

    // Only create Gemfile if user hasn't provided one
    if (!hasUserGemfile) {
      const gemfile = `source 'https://rubygems.org'

gem 'sinatra'
gem 'puma'`;

      await fs.writeFile(gemfilePath, gemfile);

      // Create basic app.rb only if user hasn't provided their own
      const appRbPath = path.join(directory, 'app.rb');
      const hasUserApp = require('fs').existsSync(appRbPath);

      if (!hasUserApp) {
        const appRb = `require 'sinatra'
require 'json'

get '/' do
  content_type :json
  { message: 'Hello from ${appData.name}!' }.to_json
end

get '/health' do
  content_type :json
  { status: 'healthy', service: '${appData.name}' }.to_json
end`;
        await fs.writeFile(appRbPath, appRb);
      }
    }

    // Install dependencies from user's Gemfile (or the one we created)
    try {
      await execAsync(`cd ${directory} && bundle install`);
      logger.info(`Installed Ruby dependencies from Gemfile for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install Ruby dependencies for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '.bundle\nvendor\n.env\n*.log\n');
    }
  }



  // =============================================================================
  // USER CRUD OPERATIONS
  // =============================================================================

  // Get all users with filtering and pagination
  async getUsers(filters: {
    page: number;
    limit: number;
    status?: string;
    plan?: string;
    server_id?: string;
    search?: string;
  }): Promise<{
    users: SharedHostingUser[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      // In production, this would query your database
      // For now, return mock data
      const mockUsers: SharedHostingUser[] = [
        {
          id: 'user_1',
          user_id: 'user_1',
          username: 'user_testuser1',
          linux_username: 'user_testuser1',
          home_directory: '/home/<USER>',
          plan: 'starter',
          status: 'active',
          server_id: this.SHARED_SERVER_IP,
          server_ip: this.SHARED_SERVER_IP,
          port: 8001,
          ssh_port: 2201,
          ftp_port: 2101,
          resource_limits: {
            cpu_quota: 5,
            memory_max: 256,
            bandwidth_limit: 25,
            storage_limit: 5
          },
          usage: {
            cpu_usage: 2.5,
            memory_usage: 128,
            bandwidth_used: 5.2,
            storage_used: 1.8
          },
          created_at: new Date('2024-01-15').toISOString(),
          applications: []
        }
      ];

      // Apply filters
      let filteredUsers = mockUsers;

      if (filters.status) {
        filteredUsers = filteredUsers.filter(user => user.status === filters.status);
      }

      if (filters.plan) {
        filteredUsers = filteredUsers.filter(user => user.plan === filters.plan);
      }

      if (filters.server_id) {
        filteredUsers = filteredUsers.filter(user => user.server_id === filters.server_id);
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.username.toLowerCase().includes(searchLower) ||
          user.linux_username.toLowerCase().includes(searchLower)
        );
      }

      // Apply pagination
      const total = filteredUsers.length;
      const totalPages = Math.ceil(total / filters.limit);
      const startIndex = (filters.page - 1) * filters.limit;
      const endIndex = startIndex + filters.limit;
      const users = filteredUsers.slice(startIndex, endIndex);

      logger.info(`Retrieved ${users.length} users (page ${filters.page}/${totalPages})`);

      return {
        users,
        total,
        page: filters.page,
        limit: filters.limit,
        totalPages
      };
    } catch (error) {
      logger.error('Failed to get users:', error);
      throw error;
    }
  }

  // Get specific user
  async getUser(userId: string): Promise<SharedHostingUser | null> {
    try {
      // In production, this would query your database
      // For now, return mock data if user exists
      if (userId === 'user_1') {
        return {
          id: 'user_1',
          user_id: 'user_1',
          username: 'user_testuser1',
          linux_username: 'user_testuser1',
          home_directory: '/home/<USER>',
          plan: 'starter',
          status: 'active',
          server_id: this.SHARED_SERVER_IP,
          server_ip: this.SHARED_SERVER_IP,
          port: 8001,
          ssh_port: 2201,
          ftp_port: 2101,
          resource_limits: {
            cpu_quota: 5,
            memory_max: 256,
            bandwidth_limit: 25,
            storage_limit: 5
          },
          usage: {
            cpu_usage: 2.5,
            memory_usage: 128,
            bandwidth_used: 5.2,
            storage_used: 1.8
          },
          created_at: new Date('2024-01-15').toISOString(),
          applications: [
            {
              id: 'app_1',
              name: 'My Website',
              type: 'static-website',
              framework: 'html',
              status: 'running',
              subdomain: 'mywebsite-user1.achidas.com',
              directory: '/home/<USER>/apps/static-website/My Website',
              ssl_enabled: false,
              domain: 'mywebsite.com',
              created_at: new Date('2024-01-16').toISOString()
            }
          ]
        };
      }

      return null;
    } catch (error) {
      logger.error(`Failed to get user ${userId}:`, error);
      throw error;
    }
  }

  // Update user
  async updateUser(userId: string, updateData: {
    plan?: string;
    status?: 'active' | 'suspended' | 'disabled';
    resource_limits?: {
      cpu_quota?: number;
      memory_max?: number;
      bandwidth_limit?: number;
      storage_limit?: number;
    };
  }): Promise<SharedHostingUser> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Update user data
      if (updateData.plan) {
        user.plan = updateData.plan;
        // Update resource limits based on new plan
        const limits = this.getPlanLimits(updateData.plan);
        user.resource_limits = {
          cpu_quota: limits.cpu_quota,
          memory_max: limits.memory_max,
          bandwidth_limit: limits.bandwidth_limit,
          storage_limit: limits.storage_limit
        };

        // Apply new resource limits via SSH
        await this.setupResourceLimits(user.linux_username, updateData.plan);
      }

      if (updateData.status) {
        user.status = updateData.status;
      }

      if (updateData.resource_limits) {
        user.resource_limits = { ...user.resource_limits, ...updateData.resource_limits };
      }

      logger.info(`Updated user ${userId}`);
      return user;
    } catch (error) {
      logger.error(`Failed to update user ${userId}:`, error);
      throw error;
    }
  }

  // Delete user
  async deleteUser(userId: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      await ssh.connect();

      // Stop all user processes
      await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);

      // Remove user and home directory
      await ssh.executeCommand(`userdel -r ${user.linux_username} || true`);

      // Remove systemd slice
      await ssh.executeCommand(`rm -rf /etc/systemd/system/${user.linux_username}.slice.d || true`);
      await ssh.executeCommand('systemctl daemon-reexec');

      logger.info(`Deleted user ${userId} and cleaned up resources`);
    } catch (error) {
      logger.error(`Failed to delete user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // =============================================================================
  // ANALYTICS AND MONITORING
  // =============================================================================

  // Get analytics data
  async getAnalytics(period: 'day' | 'week' | 'month' | 'year', serverId?: string): Promise<any> {
    try {
      // Mock analytics data - in production, this would query your monitoring system
      const analytics = {
        period,
        server_id: serverId || 'all',
        total_users: 25,
        active_users: 22,
        suspended_users: 2,
        inactive_users: 1,
        total_applications: 45,
        running_applications: 38,
        stopped_applications: 7,
        resource_usage: {
          cpu_average: 15.5,
          memory_average: 68.2,
          bandwidth_total: 1250.5,
          storage_total: 89.3
        },
        plan_distribution: {
          free: 8,
          starter: 12,
          basic: 3,
          standard: 1,
          pro: 1
        },
        revenue: {
          monthly: 245.50,
          projected_annual: 2946.00
        },
        growth: {
          new_users_this_period: 5,
          churn_rate: 2.1,
          upgrade_rate: 8.5
        }
      };

      logger.info(`Retrieved analytics for period: ${period}`);
      return analytics;
    } catch (error) {
      logger.error('Failed to get analytics:', error);
      throw error;
    }
  }

  // Get user resource usage
  async getUserResourceUsage(userId: string, period: 'day' | 'week' | 'month'): Promise<any> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Mock usage data - in production, this would query your monitoring system
      const usage = {
        user_id: userId,
        period,
        cpu_usage: [
          { timestamp: '2024-01-01T00:00:00Z', value: 2.1 },
          { timestamp: '2024-01-01T01:00:00Z', value: 3.2 },
          { timestamp: '2024-01-01T02:00:00Z', value: 1.8 }
        ],
        memory_usage: [
          { timestamp: '2024-01-01T00:00:00Z', value: 128 },
          { timestamp: '2024-01-01T01:00:00Z', value: 145 },
          { timestamp: '2024-01-01T02:00:00Z', value: 132 }
        ],
        bandwidth_usage: [
          { timestamp: '2024-01-01T00:00:00Z', value: 0.5 },
          { timestamp: '2024-01-01T01:00:00Z', value: 1.2 },
          { timestamp: '2024-01-01T02:00:00Z', value: 0.8 }
        ],
        storage_usage: user.usage.storage_used,
        limits: user.resource_limits,
        alerts: []
      };

      logger.info(`Retrieved resource usage for user: ${userId}, period: ${period}`);
      return usage;
    } catch (error) {
      logger.error(`Failed to get user resource usage for ${userId}:`, error);
      throw error;
    }
  }

  // Get server capacity
  async getServerCapacity(serverId?: string): Promise<any> {
    try {
      // Mock capacity data - in production, this would query your server monitoring
      const capacity = {
        server_id: serverId || this.SHARED_SERVER_IP,
        total_capacity: {
          cpu_cores: 4,
          memory_gb: 8,
          storage_gb: 160,
          bandwidth_gb: 1000
        },
        used_capacity: {
          cpu_percent: 35.5,
          memory_percent: 68.2,
          storage_percent: 45.8,
          bandwidth_percent: 12.5
        },
        available_capacity: {
          cpu_percent: 64.5,
          memory_percent: 31.8,
          storage_percent: 54.2,
          bandwidth_percent: 87.5
        },
        user_count: 25,
        max_users: 50,
        status: 'healthy',
        last_updated: new Date().toISOString()
      };

      logger.info(`Retrieved server capacity${serverId ? ` for server: ${serverId}` : ' for all servers'}`);
      return capacity;
    } catch (error) {
      logger.error('Failed to get server capacity:', error);
      throw error;
    }
  }

  // =============================================================================
  // USER MANAGEMENT ACTIONS
  // =============================================================================

  // Suspend user
  async suspendUser(userId: string, reason?: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      await ssh.connect();

      // Stop all user processes
      await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);

      // Lock the user account
      await ssh.executeCommand(`usermod -L ${user.linux_username}`);

      // Update user status (in production, this would update the database)
      user.status = 'suspended';

      logger.info(`Suspended user ${userId}, reason: ${reason || 'No reason provided'}`);
    } catch (error) {
      logger.error(`Failed to suspend user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Reactivate user
  async reactivateUser(userId: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      await ssh.connect();

      // Unlock the user account
      await ssh.executeCommand(`usermod -U ${user.linux_username}`);

      // Update user status (in production, this would update the database)
      user.status = 'active';

      logger.info(`Reactivated user ${userId}`);
    } catch (error) {
      logger.error(`Failed to reactivate user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Reset user password
  async resetUserPassword(userId: string, newPassword?: string): Promise<{ password: string }> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Generate random password if not provided
      const password = newPassword || Math.random().toString(36).slice(-12);

      await ssh.connect();

      // Set new password
      await ssh.executeCommand(`echo "${user.linux_username}:${password}" | chpasswd`);

      logger.info(`Reset password for user ${userId}`);
      return { password };
    } catch (error) {
      logger.error(`Failed to reset password for user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // =============================================================================
  // APPLICATION MANAGEMENT
  // =============================================================================

  // Get user applications
  async getUserApplications(userId: string, filters: {
    page: number;
    limit: number;
    status?: string;
    type?: string;
  }): Promise<{
    applications: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Mock applications data
      let applications = user.applications || [];

      // Apply filters
      if (filters.status) {
        applications = applications.filter(app => app.status === filters.status);
      }

      if (filters.type) {
        applications = applications.filter(app => app.type === filters.type);
      }

      // Apply pagination
      const total = applications.length;
      const totalPages = Math.ceil(total / filters.limit);
      const startIndex = (filters.page - 1) * filters.limit;
      const endIndex = startIndex + filters.limit;
      const paginatedApps = applications.slice(startIndex, endIndex);

      logger.info(`Retrieved ${paginatedApps.length} applications for user: ${userId}`);

      return {
        applications: paginatedApps,
        total,
        page: filters.page,
        limit: filters.limit,
        totalPages
      };
    } catch (error) {
      logger.error(`Failed to get applications for user ${userId}:`, error);
      throw error;
    }
  }

  // Update application
  async updateApplication(userId: string, appId: string, updateData: {
    name?: string;
    domain?: string;
    git_repo?: string;
    build_command?: string;
    start_command?: string;
    status?: 'running' | 'stopped';
  }): Promise<any> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const app = user.applications?.find(a => a.id === appId);
      if (!app) {
        throw new Error('Application not found');
      }

      // Update application data
      Object.assign(app, updateData);

      logger.info(`Updated application ${appId} for user: ${userId}`);
      return app;
    } catch (error) {
      logger.error(`Failed to update application ${appId} for user ${userId}:`, error);
      throw error;
    }
  }

  // Delete application
  async deleteApplication(userId: string, appId: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const appIndex = user.applications?.findIndex(a => a.id === appId);
      if (appIndex === -1 || appIndex === undefined) {
        throw new Error('Application not found');
      }

      await ssh.connect();

      // Stop application processes
      await ssh.executeCommand(`pkill -f "${appId}" || true`);

      // Remove application directory
      await ssh.executeCommand(`rm -rf ${user.home_directory}/apps/${appId} || true`);

      // Remove from user applications list
      user.applications?.splice(appIndex, 1);

      logger.info(`Deleted application ${appId} for user: ${userId}`);
    } catch (error) {
      logger.error(`Failed to delete application ${appId} for user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }
}

let sharedHostingService: SharedHostingService;

export function getSharedHostingService(): SharedHostingService {
  if (!sharedHostingService) {
    sharedHostingService = new SharedHostingService();
  }
  return sharedHostingService;
}
