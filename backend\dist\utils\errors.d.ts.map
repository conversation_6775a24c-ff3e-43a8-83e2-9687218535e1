{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/utils/errors.ts"], "names": [], "mappings": "AAGA,qBAAa,QAAS,SAAQ,KAAK;IACjC,SAAgB,UAAU,EAAE,MAAM,CAAC;IACnC,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,aAAa,EAAE,OAAO,CAAC;IACvC,SAAgB,OAAO,CAAC,EAAE,GAAG,CAAC;gBAG5B,OAAO,EAAE,MAAM,EACf,UAAU,GAAE,MAAY,EACxB,IAAI,GAAE,MAAyB,EAC/B,aAAa,GAAE,OAAc,EAC7B,OAAO,CAAC,EAAE,GAAG;CAWhB;AAED,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAkC;CAGxD;AAED,qBAAa,kBAAmB,SAAQ,QAAQ;gBAClC,OAAO,GAAE,MAAmC;CAGzD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA6B;CAGnD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA4B;CAGlD;AAED,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA8B;CAGpD;AAED,qBAAa,oBAAqB,SAAQ,QAAQ;gBACpC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,MAAiC;CAGxE;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAAoC;CAG1D;AAGD,eAAO,MAAM,WAAW;0BACA,MAAM,YAAY,GAAG;+BAChB,MAAM;8BACP,MAAM;yBACX,MAAM;yBACN,MAAM;0BACL,MAAM;+BACD,MAAM,YAAY,MAAM;yBAC9B,MAAM;wBACP,MAAM,YAAY,GAAG;CAC1C,CAAC;AAGF,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO,CAKxD;AAED,wBAAgB,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI,CAoB1D;AAGD,wBAAgB,mBAAmB,CAAC,KAAK,EAAE,KAAK;;;;;;;EAuB/C;AAGD,wBAAgB,YAAY,CAAC,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC,EAC7C,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,GAC7B,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAS5B;AAGD,qBAAa,cAAc;IAMvB,OAAO,CAAC,QAAQ,CAAC,SAAS;IAC1B,OAAO,CAAC,QAAQ,CAAC,YAAY;IAN/B,OAAO,CAAC,QAAQ,CAAa;IAC7B,OAAO,CAAC,eAAe,CAAa;IACpC,OAAO,CAAC,KAAK,CAA6C;gBAGvC,SAAS,GAAE,MAAU,EACrB,YAAY,GAAE,MAAc;IAGzC,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAmBzD,OAAO,CAAC,SAAS;IAKjB,OAAO,CAAC,SAAS;IASjB,QAAQ,IAAI,MAAM;IAIlB,WAAW,IAAI,MAAM;CAGtB;AAGD,wBAAsB,KAAK,CAAC,CAAC,EAC3B,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,WAAW,GAAE,MAAU,EACvB,KAAK,GAAE,MAAa,EACpB,OAAO,GAAE,MAAU,GAClB,OAAO,CAAC,CAAC,CAAC,CA4BZ;AAGD,wBAAgB,uBAAuB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAQ1D;AAED,wBAAgB,wBAAwB,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAQlF"}