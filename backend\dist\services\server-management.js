"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getServerManagementService = getServerManagementService;
const logger_1 = require("../utils/logger");
const exec_1 = require("../utils/exec");
class ServerManagementService {
    SHARED_SERVER_IP = '*************';
    VULTR_API_KEY = process.env.VULTR_API_KEY;
    VULTR_API_BASE = 'https://api.vultr.com/v2';
    async getSharedServerInfo() {
        try {
            logger_1.logger.info('Getting shared server information');
            const metrics = await this.getServerMetrics(this.SHARED_SERVER_IP);
            return {
                id: 'shared-server-main',
                ip_address: this.SHARED_SERVER_IP,
                hostname: 'achidas-shared-1',
                region: 'JNB',
                plan: 'vhf-1c-1gb',
                os: 'Ubuntu 25.04',
                status: 'active',
                cpu_count: 1,
                ram_mb: 1024,
                disk_gb: 25,
                bandwidth_gb: 1024,
                monthly_cost: 5.00,
                created_at: '2024-01-01T00:00:00Z',
                users_count: await this.getActiveUsersCount(),
                max_users: 50,
                load_average: metrics.load_average,
                disk_usage_percent: metrics.disk_usage,
                memory_usage_percent: metrics.memory_usage,
                network_usage: {
                    incoming_mb: metrics.network_in,
                    outgoing_mb: metrics.network_out
                }
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get shared server info:', error);
            throw error;
        }
    }
    async getServerMetrics(serverIp) {
        try {
            const cpuUsage = await this.getCPUUsage(serverIp);
            const memoryUsage = await this.getMemoryUsage(serverIp);
            const diskUsage = await this.getDiskUsage(serverIp);
            const networkStats = await this.getNetworkStats(serverIp);
            const loadAverage = await this.getLoadAverage(serverIp);
            const uptime = await this.getUptime(serverIp);
            const activeUsers = await this.getActiveUsersCount();
            return {
                cpu_usage: cpuUsage,
                memory_usage: memoryUsage,
                disk_usage: diskUsage,
                network_in: networkStats.incoming,
                network_out: networkStats.outgoing,
                load_average: loadAverage,
                uptime: uptime,
                active_users: activeUsers
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get metrics for server ${serverIp}:`, error);
            throw error;
        }
    }
    async getCPUUsage(serverIp) {
        try {
            const result = await (0, exec_1.execAsync)(`ssh root@${serverIp} "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}'"`);
            return parseFloat(result.stdout.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.warn('Failed to get CPU usage:', error);
            return 0;
        }
    }
    async getMemoryUsage(serverIp) {
        try {
            const result = await (0, exec_1.execAsync)(`ssh root@${serverIp} "free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'"`);
            return parseFloat(result.stdout.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.warn('Failed to get memory usage:', error);
            return 0;
        }
    }
    async getDiskUsage(serverIp) {
        try {
            const result = await (0, exec_1.execAsync)(`ssh root@${serverIp} "df -h / | awk 'NR==2{print $5}' | sed 's/%//g'"`);
            return parseFloat(result.stdout.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.warn('Failed to get disk usage:', error);
            return 0;
        }
    }
    async getNetworkStats(serverIp) {
        try {
            const result = await (0, exec_1.execAsync)(`ssh root@${serverIp} "cat /proc/net/dev | grep eth0 | awk '{print $2, $10}'"`);
            const [incoming, outgoing] = result.stdout.trim().split(' ').map(val => parseInt(val) / (1024 * 1024));
            return { incoming: incoming || 0, outgoing: outgoing || 0 };
        }
        catch (error) {
            logger_1.logger.warn('Failed to get network stats:', error);
            return { incoming: 0, outgoing: 0 };
        }
    }
    async getLoadAverage(serverIp) {
        try {
            const result = await (0, exec_1.execAsync)(`ssh root@${serverIp} "uptime | awk -F'load average:' '{print $2}' | sed 's/,//g'"`);
            return result.stdout.trim().split(' ').map(val => parseFloat(val.trim())).filter(val => !isNaN(val));
        }
        catch (error) {
            logger_1.logger.warn('Failed to get load average:', error);
            return [0, 0, 0];
        }
    }
    async getUptime(serverIp) {
        try {
            const result = await (0, exec_1.execAsync)(`ssh root@${serverIp} "cat /proc/uptime | awk '{print $1}'"`);
            return parseFloat(result.stdout.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.warn('Failed to get uptime:', error);
            return 0;
        }
    }
    async getActiveUsersCount() {
        try {
            const result = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "who | wc -l"`);
            return parseInt(result.stdout.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.warn('Failed to get active users count:', error);
            return 0;
        }
    }
    async provisionServer(request) {
        try {
            if (!this.VULTR_API_KEY) {
                throw new Error('Vultr API key not configured');
            }
            logger_1.logger.info(`Provisioning ${request.tier} server for user: ${request.user_id}`);
            const vultrResponse = await fetch(`${this.VULTR_API_BASE}/instances`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.VULTR_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    region: request.region,
                    plan: request.plan,
                    os_id: 387,
                    hostname: request.hostname,
                    label: `${request.tier}-${request.user_id}`,
                    tag: `achidas-${request.tier}`,
                    user_data: this.generateUserData(request.tier),
                    enable_ipv6: false,
                    enable_private_network: false,
                    auto_backups: true
                })
            });
            if (!vultrResponse.ok) {
                const error = await vultrResponse.text();
                throw new Error(`Vultr API error: ${error}`);
            }
            const vultrData = await vultrResponse.json();
            const serverInfo = {
                id: vultrData.instance.id,
                ip_address: vultrData.instance.main_ip || 'pending',
                hostname: request.hostname,
                region: request.region,
                plan: request.plan,
                os: 'Ubuntu 22.04 LTS',
                status: 'installing',
                cpu_count: this.getPlanSpecs(request.plan).cpu,
                ram_mb: this.getPlanSpecs(request.plan).ram,
                disk_gb: this.getPlanSpecs(request.plan).disk,
                bandwidth_gb: this.getPlanSpecs(request.plan).bandwidth,
                monthly_cost: this.getPlanSpecs(request.plan).cost,
                created_at: new Date().toISOString(),
                users_count: 0,
                max_users: 1
            };
            logger_1.logger.info(`Server provisioned successfully: ${serverInfo.id}`);
            return serverInfo;
        }
        catch (error) {
            logger_1.logger.error('Failed to provision server:', error);
            throw error;
        }
    }
    generateUserData(tier) {
        return `#!/bin/bash
# Achidas ${tier} server setup
apt-get update
apt-get install -y curl wget git htop nginx ufw fail2ban

# Configure firewall
ufw allow ssh
ufw allow http
ufw allow https
ufw --force enable

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Create achidas user
useradd -m -s /bin/bash achidas
usermod -aG sudo achidas
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Setup monitoring
echo "Server setup completed at $(date)" > /var/log/achidas-setup.log
`;
    }
    getPlanSpecs(plan) {
        const specs = {
            'vc2-1c-1gb': { cpu: 1, ram: 1024, disk: 25, bandwidth: 1024, cost: 5.00 },
            'vc2-2c-4gb': { cpu: 2, ram: 4096, disk: 80, bandwidth: 3072, cost: 17.28 },
            'vc2-4c-8gb': { cpu: 4, ram: 8192, disk: 160, bandwidth: 4096, cost: 34.56 },
            'vc2-8c-16gb': { cpu: 8, ram: 16384, disk: 320, bandwidth: 5120, cost: 68.40 }
        };
        return specs[plan] || specs['vc2-1c-1gb'];
    }
    async listAllServers() {
        try {
            const servers = [];
            const sharedServer = await this.getSharedServerInfo();
            servers.push(sharedServer);
            if (this.VULTR_API_KEY) {
                const vultrServers = await this.getVultrServers();
                servers.push(...vultrServers);
            }
            return servers;
        }
        catch (error) {
            logger_1.logger.error('Failed to list servers:', error);
            throw error;
        }
    }
    async getVultrServers() {
        try {
            const response = await fetch(`${this.VULTR_API_BASE}/instances`, {
                headers: {
                    'Authorization': `Bearer ${this.VULTR_API_KEY}`
                }
            });
            if (!response.ok) {
                throw new Error('Failed to fetch Vultr servers');
            }
            const data = await response.json();
            return data.instances
                .filter((instance) => instance.tag?.startsWith('achidas-'))
                .map((instance) => ({
                id: instance.id,
                ip_address: instance.main_ip,
                hostname: instance.hostname,
                region: instance.region,
                plan: instance.plan,
                os: instance.os,
                status: instance.server_status === 'ok' ? 'active' : 'pending',
                cpu_count: this.getPlanSpecs(instance.plan).cpu,
                ram_mb: this.getPlanSpecs(instance.plan).ram,
                disk_gb: this.getPlanSpecs(instance.plan).disk,
                bandwidth_gb: this.getPlanSpecs(instance.plan).bandwidth,
                monthly_cost: this.getPlanSpecs(instance.plan).cost,
                created_at: instance.date_created,
                users_count: 1,
                max_users: 1
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to get Vultr servers:', error);
            return [];
        }
    }
}
let serverManagementService;
function getServerManagementService() {
    if (!serverManagementService) {
        serverManagementService = new ServerManagementService();
    }
    return serverManagementService;
}
//# sourceMappingURL=server-management.js.map