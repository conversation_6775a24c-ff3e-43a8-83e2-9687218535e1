"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseConnection = exports.DatabaseConnection = void 0;
const mongodb_1 = require("mongodb");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
class DatabaseConnection {
    client = null;
    database = null;
    async connect() {
        try {
            logger_1.logger.info('Connecting to MongoDB...');
            this.client = new mongodb_1.MongoClient(config_1.appConfig.database.url, {
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
                family: 4,
            });
            await this.client.connect();
            const dbName = this.extractDatabaseName(config_1.appConfig.database.url) || 'achidas';
            this.database = this.client.db(dbName);
            await this.database.admin().ping();
            logger_1.logger.info(`Successfully connected to MongoDB database: ${dbName}`);
        }
        catch (error) {
            logger_1.logger.error('Failed to connect to MongoDB:', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.client) {
            await this.client.close();
            this.client = null;
            this.database = null;
            logger_1.logger.info('Disconnected from MongoDB');
        }
    }
    getDatabase() {
        if (!this.database) {
            throw new Error('Database not connected. Call connect() first.');
        }
        return this.database;
    }
    getCollection(name) {
        return this.getDatabase().collection(name);
    }
    extractDatabaseName(url) {
        try {
            const match = url.match(/\/([^/?]+)(\?|$)/);
            return match ? match[1] || null : null;
        }
        catch {
            return null;
        }
    }
    async createIndexes() {
        try {
            const db = this.getDatabase();
            const usersCollection = db.collection('users');
            await usersCollection.createIndex({ email: 1 }, { unique: true });
            await usersCollection.createIndex({ username: 1 }, { unique: true });
            await usersCollection.createIndex({ status: 1 });
            await usersCollection.createIndex({ created_at: 1 });
            const applicationsCollection = db.collection('applications');
            await applicationsCollection.createIndex({ user_id: 1 });
            await applicationsCollection.createIndex({ name: 1, user_id: 1 }, { unique: true });
            await applicationsCollection.createIndex({ status: 1 });
            await applicationsCollection.createIndex({ created_at: 1 });
            const serversCollection = db.collection('servers');
            await serversCollection.createIndex({ vultr_id: 1 }, { unique: true });
            await serversCollection.createIndex({ status: 1 });
            await serversCollection.createIndex({ region: 1 });
            await serversCollection.createIndex({ plan: 1 });
            logger_1.logger.info('Database indexes created successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to create database indexes:', error);
            throw error;
        }
    }
}
exports.DatabaseConnection = DatabaseConnection;
exports.databaseConnection = new DatabaseConnection();
//# sourceMappingURL=connection.js.map