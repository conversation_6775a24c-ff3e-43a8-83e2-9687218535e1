"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = authMiddleware;
exports.adminMiddleware = adminMiddleware;
exports.superAdminMiddleware = superAdminMiddleware;
exports.optionalAuthMiddleware = optionalAuthMiddleware;
exports.createOwnershipMiddleware = createOwnershipMiddleware;
exports.createUserRateLimitMiddleware = createUserRateLimitMiddleware;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config");
const response_1 = require("../utils/response");
const models_1 = require("../models");
const logger_1 = require("../utils/logger");
async function authMiddleware(request, reply) {
    try {
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authorization token required');
        }
        const token = authHeader.substring(7);
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.appConfig.auth.jwtSecret);
            request.user = decoded;
        }
        catch (jwtError) {
            logger_1.logger.warn('Invalid JWT token:', jwtError);
            return response_1.ResponseHelper.unauthorized(reply, 'Invalid or expired token');
        }
    }
    catch (error) {
        logger_1.logger.error('Auth middleware error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Authentication error');
    }
}
async function adminMiddleware(request, reply) {
    if (!request.user) {
        return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
    }
    if (request.user.role !== models_1.UserRole.ADMIN && request.user.role !== models_1.UserRole.SUPER_ADMIN) {
        return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
    }
}
async function superAdminMiddleware(request, reply) {
    if (!request.user) {
        return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
    }
    if (request.user.role !== models_1.UserRole.SUPER_ADMIN) {
        return response_1.ResponseHelper.forbidden(reply, 'Super admin access required');
    }
}
async function optionalAuthMiddleware(request, _reply) {
    try {
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            try {
                const decoded = jsonwebtoken_1.default.verify(token, config_1.appConfig.auth.jwtSecret);
                request.user = decoded;
            }
            catch (jwtError) {
                logger_1.logger.debug('Optional auth - invalid token:', jwtError);
            }
        }
    }
    catch (error) {
        logger_1.logger.error('Optional auth middleware error:', error);
    }
}
function createOwnershipMiddleware(userIdParam = 'userId') {
    return async function ownershipMiddleware(request, reply) {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const params = request.params;
        const resourceUserId = params[userIdParam];
        if (request.user.role === models_1.UserRole.SUPER_ADMIN) {
            return;
        }
        if (request.user.sub !== resourceUserId) {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied to this resource');
        }
    };
}
function createUserRateLimitMiddleware(maxRequests = 100, windowMs = 60000) {
    const userRequests = new Map();
    return async function userRateLimitMiddleware(request, reply) {
        if (!request.user) {
            return;
        }
        const userId = request.user.sub;
        const now = Date.now();
        const userLimit = userRequests.get(userId);
        if (!userLimit || now > userLimit.resetTime) {
            userRequests.set(userId, {
                count: 1,
                resetTime: now + windowMs,
            });
            return;
        }
        if (userLimit.count >= maxRequests) {
            return response_1.ResponseHelper.error(reply, 'RATE_LIMIT_EXCEEDED', 'Too many requests. Please try again later.', 429);
        }
        userLimit.count++;
    };
}
//# sourceMappingURL=auth.js.map