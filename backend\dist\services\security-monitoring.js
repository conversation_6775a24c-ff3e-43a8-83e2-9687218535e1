"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSecurityMonitoringService = getSecurityMonitoringService;
const logger_1 = require("../utils/logger");
const exec_1 = require("../utils/exec");
class SecurityMonitoringService {
    SHARED_SERVER_IP = '*************';
    alerts = [];
    async monitorUserIsolation() {
        try {
            logger_1.logger.info('Starting user isolation security monitoring');
            const users = await this.getSharedHostingUsers();
            const statuses = [];
            for (const user of users) {
                const status = await this.checkUserIsolation(user);
                statuses.push(status);
                if (status.isolation_status !== 'secure') {
                    await this.generateSecurityAlert(user, status);
                }
            }
            logger_1.logger.info(`Monitored ${statuses.length} users for security violations`);
            return statuses;
        }
        catch (error) {
            logger_1.logger.error('Failed to monitor user isolation:', error);
            throw error;
        }
    }
    async checkUserIsolation(user) {
        try {
            const filePermissionsOk = await this.checkFilePermissions(user.linux_username);
            const processLimitsOk = await this.checkProcessLimits(user.linux_username);
            const networkRestrictionsOk = await this.checkNetworkRestrictions(user.linux_username);
            const resourceUsage = await this.getUserResourceUsage(user.linux_username);
            const violations = [];
            if (!filePermissionsOk)
                violations.push('File permissions compromised');
            if (!processLimitsOk)
                violations.push('Process limits exceeded');
            if (!networkRestrictionsOk)
                violations.push('Network restrictions violated');
            let isolationStatus = 'secure';
            if (violations.length > 0) {
                isolationStatus = violations.length >= 2 ? 'compromised' : 'warning';
            }
            return {
                user_id: user.user_id,
                username: user.username,
                linux_username: user.linux_username,
                isolation_status: isolationStatus,
                file_permissions_ok: filePermissionsOk,
                process_limits_ok: processLimitsOk,
                network_restrictions_ok: networkRestrictionsOk,
                resource_usage: resourceUsage,
                last_checked: new Date().toISOString(),
                violations
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to check isolation for user ${user.username}:`, error);
            throw error;
        }
    }
    async checkFilePermissions(linuxUsername) {
        try {
            const homeDir = `/var/www/user_${linuxUsername}`;
            const homeDirPerms = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "stat -c '%a' ${homeDir}"`);
            if (homeDirPerms.stdout.trim() !== '700') {
                logger_1.logger.warn(`Home directory permissions incorrect for ${linuxUsername}: ${homeDirPerms.stdout.trim()}`);
                return false;
            }
            const accessTest = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "sudo -u ${linuxUsername} ls /var/www/ | wc -l"`).catch(() => null);
            if (accessTest && parseInt(accessTest.stdout.trim()) > 1) {
                logger_1.logger.warn(`User ${linuxUsername} can access other user directories`);
                return false;
            }
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to check file permissions for ${linuxUsername}:`, error);
            return false;
        }
    }
    async checkProcessLimits(linuxUsername) {
        try {
            const sliceStatus = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "systemctl is-active user-${linuxUsername}.slice"`);
            if (sliceStatus.stdout.trim() !== 'active') {
                logger_1.logger.warn(`Systemd slice not active for ${linuxUsername}`);
                return false;
            }
            const cpuUsage = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "systemctl show user-${linuxUsername}.slice --property=CPUUsageNSec"`);
            const memoryUsage = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "systemctl show user-${linuxUsername}.slice --property=MemoryCurrent"`);
            return cpuUsage.stdout.includes('CPUUsageNSec=') && memoryUsage.stdout.includes('MemoryCurrent=');
        }
        catch (error) {
            logger_1.logger.error(`Failed to check process limits for ${linuxUsername}:`, error);
            return false;
        }
    }
    async checkNetworkRestrictions(linuxUsername) {
        try {
            const tcRules = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "tc qdisc show dev eth0 | grep ${linuxUsername}"`).catch(() => null);
            const netConnections = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "netstat -tulpn | grep ${linuxUsername}"`).catch(() => null);
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Failed to check network restrictions for ${linuxUsername}:`, error);
            return false;
        }
    }
    async getUserResourceUsage(linuxUsername) {
        try {
            const cpuUsage = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "ps -u ${linuxUsername} -o %cpu --no-headers | awk '{sum += $1} END {print sum}'"`).catch(() => ({ stdout: '0' }));
            const memoryUsage = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "ps -u ${linuxUsername} -o %mem --no-headers | awk '{sum += $1} END {print sum}'"`).catch(() => ({ stdout: '0' }));
            const diskUsage = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "du -sm /var/www/user_${linuxUsername} | cut -f1"`).catch(() => ({ stdout: '0' }));
            return {
                cpu_percent: parseFloat(cpuUsage.stdout.trim()) || 0,
                memory_percent: parseFloat(memoryUsage.stdout.trim()) || 0,
                disk_usage_mb: parseInt(diskUsage.stdout.trim()) || 0,
                network_usage_mb: 0
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get resource usage for ${linuxUsername}:`, error);
            return { cpu_percent: 0, memory_percent: 0, disk_usage_mb: 0, network_usage_mb: 0 };
        }
    }
    async generateSecurityAlert(user, status) {
        const alert = {
            id: `alert_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            type: status.isolation_status === 'compromised' ? 'unauthorized_access' : 'file_permission',
            severity: status.isolation_status === 'compromised' ? 'high' : 'medium',
            user_id: user.user_id,
            username: user.username,
            description: `User isolation ${status.isolation_status} detected`,
            details: {
                violations: status.violations,
                file_permissions_ok: status.file_permissions_ok,
                process_limits_ok: status.process_limits_ok,
                network_restrictions_ok: status.network_restrictions_ok,
                resource_usage: status.resource_usage
            },
            timestamp: new Date().toISOString(),
            resolved: false,
            actions_taken: []
        };
        this.alerts.push(alert);
        logger_1.logger.warn(`Security alert generated: ${alert.id} for user ${user.username}`);
        if (alert.severity === 'high' || alert.severity === 'critical') {
            await this.autoRemediate(alert);
        }
    }
    async autoRemediate(alert) {
        try {
            const actions = [];
            if (alert.type === 'unauthorized_access' || alert.type === 'file_permission') {
                await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "chmod 700 /var/www/user_${alert.username}"`);
                await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "chown -R ${alert.username}:${alert.username} /var/www/user_${alert.username}"`);
                actions.push('Fixed file permissions');
            }
            if (alert.type === 'resource_abuse') {
                await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "systemctl restart user-${alert.username}.slice"`);
                actions.push('Restarted resource limits');
            }
            alert.actions_taken = actions;
            logger_1.logger.info(`Auto-remediation completed for alert ${alert.id}: ${actions.join(', ')}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to auto-remediate alert ${alert.id}:`, error);
        }
    }
    async getSharedHostingUsers() {
        try {
            const userList = await (0, exec_1.execAsync)(`ssh root@${this.SHARED_SERVER_IP} "ls /var/www/ | grep user_"`);
            const userDirs = userList.stdout.trim().split('\n').filter(dir => dir.startsWith('user_'));
            return userDirs.map(dir => {
                const username = dir.replace('user_', '');
                return {
                    user_id: `user_${username}`,
                    username: username,
                    linux_username: username
                };
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to get shared hosting users:', error);
            return [];
        }
    }
    async getSecurityMetrics() {
        try {
            const statuses = await this.monitorUserIsolation();
            const activeAlerts = this.alerts.filter(alert => !alert.resolved).length;
            const resolved24h = this.alerts.filter(alert => alert.resolved &&
                new Date(alert.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)).length;
            const secureUsers = statuses.filter(s => s.isolation_status === 'secure').length;
            const warningUsers = statuses.filter(s => s.isolation_status === 'warning').length;
            const compromisedUsers = statuses.filter(s => s.isolation_status === 'compromised').length;
            const healthScore = statuses.length > 0 ? (secureUsers / statuses.length) * 100 : 100;
            return {
                total_users: statuses.length,
                secure_users: secureUsers,
                warning_users: warningUsers,
                compromised_users: compromisedUsers,
                active_alerts: activeAlerts,
                resolved_alerts_24h: resolved24h,
                system_health_score: Math.round(healthScore)
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get security metrics:', error);
            throw error;
        }
    }
    getAlerts(resolved) {
        if (resolved !== undefined) {
            return this.alerts.filter(alert => alert.resolved === resolved);
        }
        return this.alerts;
    }
    async resolveAlert(alertId, resolution) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.resolved = true;
            alert.actions_taken.push(`Manually resolved: ${resolution}`);
            logger_1.logger.info(`Alert ${alertId} resolved: ${resolution}`);
        }
    }
}
let securityMonitoringService;
function getSecurityMonitoringService() {
    if (!securityMonitoringService) {
        securityMonitoringService = new SecurityMonitoringService();
    }
    return securityMonitoringService;
}
//# sourceMappingURL=security-monitoring.js.map