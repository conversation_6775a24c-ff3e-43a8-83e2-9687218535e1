import { FastifyInstance } from 'fastify';
import {
  getHostingPlansController,
  getHostingPlanController,
  getRecommendedPlanController,
  createSharedUserController,
  createApplicationController,
} from '../controllers/hosting.controller';
import { authMiddleware } from '../middleware/auth';

export async function hostingRoutes(fastify: FastifyInstance): Promise<void> {
  // Public hosting plan routes (no authentication required)
  fastify.get('/plans', getHostingPlansController);
  fastify.get('/plans/recommend', getRecommendedPlanController);
  fastify.get('/plans/:planName', getHostingPlanController);

  // Shared hosting management routes (authentication required)
  fastify.register(async function (fastify) {
    // Add authentication middleware
    fastify.addHook('preHandler', authMiddleware);

    // Shared hosting user management
    fastify.post('/shared/users', createSharedUserController);

    // Application management for shared users
    fastify.post('/shared/users/:userId/applications', createApplicationController);
  }, { prefix: '/manage' });
}
