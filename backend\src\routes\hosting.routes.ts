import { FastifyInstance } from 'fastify';
import {
  getHostingPlansController,
  getHostingPlanController,
  getRecommendedPlanController,
} from '../controllers/hosting.controller';

export async function hostingRoutes(fastify: FastifyInstance): Promise<void> {

  fastify.get('/plans', getHostingPlansController);
  fastify.get('/plans/recommend', getRecommendedPlanController);
  fastify.get('/plans/:planName', getHostingPlanController);
}
