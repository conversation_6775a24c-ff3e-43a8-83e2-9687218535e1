import { ObjectId } from 'mongodb';
import { z } from 'zod';
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    PENDING = "pending"
}
export declare enum UserRole {
    USER = "user",
    ADMIN = "admin",
    SUPER_ADMIN = "super_admin"
}
export interface User {
    _id?: ObjectId;
    id?: string;
    email: string;
    username: string;
    password_hash: string;
    first_name: string;
    last_name: string;
    company?: string;
    role: UserRole;
    status: UserStatus;
    email_verified: boolean;
    phone?: string;
    avatar_url?: string;
    last_login?: Date;
    created_at: Date;
    updated_at: Date;
    metadata?: {
        login_attempts: number;
        last_login_ip?: string;
        preferences?: Record<string, any>;
    };
}
export interface UserResponse {
    id: string;
    email: string;
    username: string;
    first_name: string;
    last_name: string;
    company?: string | undefined;
    role: UserRole;
    status: UserStatus;
    email_verified: boolean;
    phone?: string | undefined;
    avatar_url?: string | undefined;
    last_login?: string | undefined;
    created_at: string;
    updated_at: string;
}
export declare const createUserSchema: z.ZodObject<{
    email: z.ZodString;
    username: z.ZodString;
    password: z.ZodString;
    first_name: z.ZodString;
    last_name: z.ZodString;
    company: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    email: string;
    username: string;
    password: string;
    first_name: string;
    last_name: string;
    company?: string | undefined;
    phone?: string | undefined;
}, {
    email: string;
    username: string;
    password: string;
    first_name: string;
    last_name: string;
    company?: string | undefined;
    phone?: string | undefined;
}>;
export declare const loginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
}, {
    email: string;
    password: string;
}>;
export declare const updateUserSchema: z.ZodObject<{
    first_name: z.ZodOptional<z.ZodString>;
    last_name: z.ZodOptional<z.ZodString>;
    company: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    avatar_url: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    first_name?: string | undefined;
    last_name?: string | undefined;
    company?: string | undefined;
    phone?: string | undefined;
    avatar_url?: string | undefined;
}, {
    first_name?: string | undefined;
    last_name?: string | undefined;
    company?: string | undefined;
    phone?: string | undefined;
    avatar_url?: string | undefined;
}>;
export declare const changePasswordSchema: z.ZodObject<{
    current_password: z.ZodString;
    new_password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    current_password: string;
    new_password: string;
}, {
    current_password: string;
    new_password: string;
}>;
export type CreateUserRequest = z.infer<typeof createUserSchema>;
export type LoginRequest = z.infer<typeof loginSchema>;
export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type ChangePasswordRequest = z.infer<typeof changePasswordSchema>;
export declare function toUserResponse(user: User): UserResponse;
//# sourceMappingURL=user.d.ts.map