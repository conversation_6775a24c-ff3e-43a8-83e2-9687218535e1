{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/utils/errors.ts"], "names": [], "mappings": ";;;AAyFA,gDAKC;AAED,4BAoBC;AAGD,kDAuBC;AAGD,oCAWC;AAwDD,sBAiCC;AAGD,0DAQC;AAED,4DAQC;AA1QD,qCAAkC;AAGlC,MAAa,QAAS,SAAQ,KAAK;IACjB,UAAU,CAAS;IACnB,IAAI,CAAS;IACb,aAAa,CAAU;IACvB,OAAO,CAAO;IAE9B,YACE,OAAe,EACf,aAAqB,GAAG,EACxB,OAAe,gBAAgB,EAC/B,gBAAyB,IAAI,EAC7B,OAAa;QAEb,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAtBD,4BAsBC;AAED,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;CACF;AAJD,0CAIC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,yBAAyB;QACrD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,0BAA0B;QACtD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,oBAAoB;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,qBAAqB;QACjD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,wCAIC;AAED,MAAa,oBAAqB,SAAQ,QAAQ;IAChD,YAAY,OAAe,EAAE,UAAkB,wBAAwB;QACrE,KAAK,CAAC,GAAG,OAAO,KAAK,OAAO,EAAE,EAAE,GAAG,EAAE,wBAAwB,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACpF,CAAC;CACF;AAJD,oDAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,2BAA2B;QACvD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,sCAIC;AAGY,QAAA,WAAW,GAAG;IACzB,UAAU,EAAE,CAAC,OAAe,EAAE,OAAa,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC;IACrF,cAAc,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,OAAO,CAAC;IACtE,aAAa,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,OAAO,CAAC;IACpE,QAAQ,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC;IAC1D,QAAQ,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC;IAC1D,SAAS,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC;IAC5D,eAAe,EAAE,CAAC,OAAe,EAAE,OAAgB,EAAE,EAAE,CAAC,IAAI,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC;IAClG,QAAQ,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC;IAC1D,QAAQ,EAAE,CAAC,OAAe,EAAE,OAAa,EAAE,EAAE,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,CAAC;CAC1G,CAAC;AAGF,SAAgB,kBAAkB,CAAC,KAAY;IAC7C,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC,aAAa,CAAC;IAC7B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,QAAQ,CAAC,KAAY,EAAE,OAAa;IAClD,MAAM,SAAS,GAAG;QAChB,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,GAAG,OAAO;KACX,CAAC;IAEF,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QACxC,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAC5B,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QAC9C,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IACpC,CAAC;IAED,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;IAC/C,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAGD,SAAgB,mBAAmB,CAAC,KAAY;IAC9C,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB;SACF,CAAC;IACJ,CAAC;IAGD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY,CAAC;IAE9D,OAAO;QACL,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,gBAAgB;YACtB,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO;YACtE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE;SAC3D;KACF,CAAC;AACJ,CAAC;AAGD,SAAgB,YAAY,CAC1B,EAA8B;IAE9B,OAAO,KAAK,EAAE,GAAG,IAAO,EAAc,EAAE;QACtC,IAAI,CAAC;YACH,OAAO,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,KAAc,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAGD,MAAa,cAAc;IAMN;IACA;IANX,QAAQ,GAAW,CAAC,CAAC;IACrB,eAAe,GAAW,CAAC,CAAC;IAC5B,KAAK,GAAoC,QAAQ,CAAC;IAE1D,YACmB,YAAoB,CAAC,EACrB,eAAuB,KAAK;QAD5B,cAAS,GAAT,SAAS,CAAY;QACrB,iBAAY,GAAZ,YAAY,CAAgB;IAC5C,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAI,SAA2B;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC1D,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,MAAM,mBAAW,CAAC,eAAe,CAAC,iBAAiB,EAAE,iCAAiC,CAAC,CAAC;YAC1F,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACxB,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QACtB,CAAC;IACH,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;AAlDD,wCAkDC;AAGM,KAAK,UAAU,KAAK,CACzB,SAA2B,EAC3B,cAAsB,CAAC,EACvB,QAAgB,IAAI,EACpB,UAAkB,CAAC;IAEnB,IAAI,SAAgB,CAAC;IAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;QACxD,IAAI,CAAC;YACH,OAAO,MAAM,SAAS,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,GAAG,KAAc,CAAC;YAE3B,IAAI,OAAO,KAAK,WAAW,EAAE,CAAC;gBAC5B,MAAM;YACR,CAAC;YAGD,IAAI,KAAK,YAAY,QAAQ,IAAI,KAAK,CAAC,UAAU,IAAI,GAAG,IAAI,KAAK,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBACnF,MAAM;YACR,CAAC;YAED,MAAM,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;YACxD,eAAM,CAAC,IAAI,CAAC,iCAAiC,QAAQ,eAAe,OAAO,IAAI,WAAW,IAAI,EAAE;gBAC9F,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YAEH,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,MAAM,SAAU,CAAC;AACnB,CAAC;AAGD,SAAgB,uBAAuB,CAAC,KAAY;IAClD,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;QAClC,KAAK,EAAE,KAAK,CAAC,OAAO;QACpB,KAAK,EAAE,KAAK,CAAC,KAAK;KACnB,CAAC,CAAC;IAGH,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,SAAgB,wBAAwB,CAAC,MAAW,EAAE,QAAsB;IAC1E,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;QAC3C,MAAM,EAAE,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;QACjE,KAAK,EAAE,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;KAC1D,CAAC,CAAC;IAGH,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC"}