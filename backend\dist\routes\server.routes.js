"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverRoutes = serverRoutes;
const auth_1 = require("../middleware/auth");
const server_controller_1 = require("../controllers/server.controller");
async function serverRoutes(fastify) {
    fastify.get('/regions', server_controller_1.getServerRegionsController);
    fastify.get('/plans', server_controller_1.getServerPlansController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.authMiddleware);
        fastify.get('/', server_controller_1.getServersController);
        fastify.get('/shared/status', server_controller_1.getSharedServerStatusController);
        fastify.get('/:serverId', server_controller_1.getServerController);
        fastify.get('/:serverId/metrics', server_controller_1.getServerMetricsController);
        fastify.post('/provision', server_controller_1.provisionServerController);
    });
}
//# sourceMappingURL=server.routes.js.map