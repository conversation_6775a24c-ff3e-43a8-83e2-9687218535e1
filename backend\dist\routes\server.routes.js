"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverRoutes = serverRoutes;
const auth_1 = require("../middleware/auth");
const server_controller_1 = require("../controllers/server.controller");
const hosting_controller_1 = require("../controllers/hosting.controller");
const application_controller_1 = require("../controllers/application.controller");
async function serverRoutes(fastify) {
    fastify.get('/regions', server_controller_1.getServerRegionsController);
    fastify.get('/plans', server_controller_1.getServerPlansController);
    fastify.get('/hosting/plans', hosting_controller_1.getHostingPlansController);
    fastify.get('/hosting/plans/recommend', hosting_controller_1.getRecommendedPlanController);
    fastify.get('/hosting/plans/:planName', hosting_controller_1.getHostingPlanController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.authMiddleware);
        fastify.get('/', server_controller_1.getServersController);
        fastify.get('/shared/status', server_controller_1.getSharedServerStatusController);
        fastify.get('/:serverId', server_controller_1.getServerController);
        fastify.get('/:serverId/metrics', server_controller_1.getServerMetricsController);
        fastify.post('/provision', server_controller_1.provisionServerController);
        fastify.post('/hosting/shared/users', hosting_controller_1.createSharedUserController);
        fastify.post('/hosting/shared/users/:userId/applications', hosting_controller_1.createApplicationController);
        fastify.post('/applications', application_controller_1.createApplicationController);
        fastify.get('/applications', application_controller_1.getApplicationsController);
        fastify.get('/applications/stats', application_controller_1.getApplicationStatsController);
        fastify.get('/applications/:id', application_controller_1.getApplicationController);
        fastify.put('/applications/:id', application_controller_1.updateApplicationController);
        fastify.delete('/applications/:id', application_controller_1.deleteApplicationController);
        fastify.post('/applications/:id/deploy', application_controller_1.deployApplicationController);
        fastify.post('/applications/:id/stop', application_controller_1.stopApplicationController);
    });
}
//# sourceMappingURL=server.routes.js.map