import { FastifyRequest, FastifyReply } from 'fastify';
import { 
  CreateUserRequest, 
  LoginRequest, 
  ChangePasswordRequest,
  createUserSchema,
  loginSchema,
  changePasswordSchema
} from '../models';
import { getAuthService } from '../services/auth';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';

// Register controller
export async function registerController(
  request: FastifyRequest<{ Body: CreateUserRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    // Validate request body
    const validationResult = createUserSchema.safeParse(request.body);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      }));
      
      return ResponseHelper.validationError(
        reply,
        'Validation failed',
        { errors }
      );
    }

    const userData = validationResult.data;

    // Register user
    const user = await getAuthService().registerUser(userData);

    logger.info(`User registered: ${user.email}`);

    return ResponseHelper.success(reply, user, 201);
  } catch (error) {
    logger.error('Register controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return ResponseHelper.error(reply, 'CONFLICT', error.message, 409);
      }
    }

    return ResponseHelper.internalError(reply, 'Registration failed');
  }
}

// Login controller
export async function loginController(
  request: FastifyRequest<{ Body: LoginRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    // Validate request body
    const validationResult = loginSchema.safeParse(request.body);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      }));
      
      return ResponseHelper.validationError(
        reply,
        'Validation failed',
        { errors }
      );
    }

    const loginData = validationResult.data;

    // Login user
    const loginResponse = await getAuthService().loginUser(loginData);

    // Update last login with IP address
    const clientIp = request.ip || request.headers['x-forwarded-for'] as string || 'unknown';
    await getAuthService().updateUserLastLogin(loginResponse.user.id, clientIp);

    logger.info(`User logged in: ${loginData.email}`);

    return ResponseHelper.success(reply, loginResponse);
  } catch (error) {
    logger.error('Login controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid email or password') || 
          error.message.includes('Account is not active')) {
        return ResponseHelper.unauthorized(reply, error.message);
      }
    }

    return ResponseHelper.internalError(reply, 'Login failed');
  }
}

// Get current user profile controller
export async function getCurrentUserController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const user = await getAuthService().getUserById(request.user.sub);

    return ResponseHelper.success(reply, user);
  } catch (error) {
    logger.error('Get current user controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'User not found');
    }

    return ResponseHelper.internalError(reply, 'Failed to get user profile');
  }
}

// Get user profile controller (alias for getCurrentUserController)
export async function getProfileController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  return getCurrentUserController(request, reply);
}

// Change password controller
export async function changePasswordController(
  request: FastifyRequest<{ Body: ChangePasswordRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    // Validate request body
    const validationResult = changePasswordSchema.safeParse(request.body);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      }));
      
      return ResponseHelper.validationError(
        reply,
        'Validation failed',
        { errors }
      );
    }

    const { current_password, new_password } = validationResult.data;

    // Change password
    await getAuthService().changePassword(request.user.sub, current_password, new_password);

    logger.info(`Password changed for user: ${request.user.email}`);

    return ResponseHelper.success(reply, { message: 'Password changed successfully' });
  } catch (error) {
    logger.error('Change password controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Current password is incorrect')) {
        return ResponseHelper.error(reply, 'INVALID_PASSWORD', error.message, 400);
      }
      if (error.message.includes('not found')) {
        return ResponseHelper.notFound(reply, 'User not found');
      }
    }

    return ResponseHelper.internalError(reply, 'Failed to change password');
  }
}

// Logout controller (client-side token invalidation)
export async function logoutController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    // In a stateless JWT system, logout is typically handled client-side
    // by removing the token. We can log the logout event here.
    
    if (request.user) {
      logger.info(`User logged out: ${request.user.email}`);
    }

    return ResponseHelper.success(reply, { 
      message: 'Logged out successfully. Please remove the token from client storage.' 
    });
  } catch (error) {
    logger.error('Logout controller error:', error);
    return ResponseHelper.internalError(reply, 'Logout failed');
  }
}

// Verify token controller
export async function verifyTokenController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Invalid or expired token');
    }

    return ResponseHelper.success(reply, {
      valid: true,
      user: {
        id: request.user.sub,
        email: request.user.email,
        username: request.user.username,
        role: request.user.role,
      },
    });
  } catch (error) {
    logger.error('Verify token controller error:', error);
    return ResponseHelper.internalError(reply, 'Token verification failed');
  }
}
