import { FastifyInstance, FastifyError, FastifyRequest, FastifyReply } from 'fastify';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';

export interface CustomError extends Error {
  statusCode?: number;
  code?: string;
  validation?: any[];
}

export async function errorHandler(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  logger.error('Request error:', {
    error: error.message,
    stack: error.stack,
    url: request.url,
    method: request.method,
    statusCode: error.statusCode,
    code: error.code,
  });

  // Handle validation errors
  if (error.validation) {
    const validationErrors = error.validation.map((err: any) => ({
      field: err.instancePath || err.dataPath || 'unknown',
      message: err.message || 'Validation failed',
      value: err.data,
    }));

    return ResponseHelper.validationError(
      reply,
      'Request validation failed',
      { errors: validationErrors }
    );
  }

  // Handle specific error types
  switch (error.code) {
    case 'FST_ERR_VALIDATION':
      return ResponseHelper.validationError(reply, error.message);

    case 'FST_JWT_NO_AUTHORIZATION_IN_HEADER':
    case 'FST_JWT_AUTHORIZATION_TOKEN_INVALID':
      return ResponseHelper.unauthorized(reply, 'Invalid or missing authorization token');

    case 'FST_ERR_NOT_FOUND':
      return ResponseHelper.notFound(reply, 'Resource not found');

    case 'ECONNREFUSED':
    case 'ENOTFOUND':
      return ResponseHelper.internalError(reply, 'External service unavailable');

    default:
      break;
  }

  // Handle HTTP status codes
  const statusCode = error.statusCode || 500;

  switch (statusCode) {
    case 400:
      return ResponseHelper.error(reply, 'BAD_REQUEST', error.message, 400);

    case 401:
      return ResponseHelper.unauthorized(reply, error.message || 'Unauthorized');

    case 403:
      return ResponseHelper.forbidden(reply, error.message || 'Forbidden');

    case 404:
      return ResponseHelper.notFound(reply, error.message || 'Not found');

    case 409:
      return ResponseHelper.error(reply, 'CONFLICT', error.message, 409);

    case 422:
      return ResponseHelper.validationError(reply, error.message || 'Unprocessable entity');

    case 429:
      return ResponseHelper.error(reply, 'RATE_LIMIT_EXCEEDED', 'Too many requests', 429);

    default:
      return ResponseHelper.internalError(reply, 'An unexpected error occurred');
  }
}

export function registerErrorHandler(fastify: FastifyInstance): void {
  fastify.setErrorHandler(errorHandler);
}
