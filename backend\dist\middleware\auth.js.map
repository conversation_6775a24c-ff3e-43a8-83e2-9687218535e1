{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;AAeA,wCAwBC;AAGD,0CAWC;AAGD,oDAWC;AAGD,wDAsBC;AAGD,8DAsBC;AAGD,sEAmCC;AA1JD,gEAA+B;AAC/B,sCAAsC;AACtC,gDAAmD;AACnD,sCAAiD;AACjD,4CAAyC;AAUlC,KAAK,UAAU,cAAc,CAClC,OAAuB,EACvB,KAAmB;IAEnB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;QAEjD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,yBAAc,CAAC,YAAY,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,kBAAS,CAAC,IAAI,CAAC,SAAS,CAAe,CAAC;YAC1E,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;QACzB,CAAC;QAAC,OAAO,QAAQ,EAAE,CAAC;YAClB,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;YAC5C,OAAO,yBAAc,CAAC,YAAY,CAAC,KAAK,EAAE,0BAA0B,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC9C,OAAO,yBAAc,CAAC,aAAa,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;IACrE,CAAC;AACH,CAAC;AAGM,KAAK,UAAU,eAAe,CACnC,OAAuB,EACvB,KAAmB;IAEnB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,yBAAc,CAAC,YAAY,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;IACvE,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,iBAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,iBAAQ,CAAC,WAAW,EAAE,CAAC;QACvF,OAAO,yBAAc,CAAC,SAAS,CAAC,KAAK,EAAE,uBAAuB,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAGM,KAAK,UAAU,oBAAoB,CACxC,OAAuB,EACvB,KAAmB;IAEnB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,yBAAc,CAAC,YAAY,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;IACvE,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,iBAAQ,CAAC,WAAW,EAAE,CAAC;QAC/C,OAAO,yBAAc,CAAC,SAAS,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;IACxE,CAAC;AACH,CAAC;AAGM,KAAK,UAAU,sBAAsB,CAC1C,OAAuB,EACvB,MAAoB;IAEpB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC;QAEjD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,kBAAS,CAAC,IAAI,CAAC,SAAS,CAAe,CAAC;gBAC1E,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;YACzB,CAAC;YAAC,OAAO,QAAQ,EAAE,CAAC;gBAElB,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;IAEzD,CAAC;AACH,CAAC;AAGD,SAAgB,yBAAyB,CAAC,cAAsB,QAAQ;IACtE,OAAO,KAAK,UAAU,mBAAmB,CACvC,OAAuB,EACvB,KAAmB;QAEnB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,OAAO,yBAAc,CAAC,YAAY,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAgC,CAAC;QACxD,MAAM,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;QAG3C,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,iBAAQ,CAAC,WAAW,EAAE,CAAC;YAC/C,OAAO;QACT,CAAC;QAGD,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,cAAc,EAAE,CAAC;YACxC,OAAO,yBAAc,CAAC,SAAS,CAAC,KAAK,EAAE,gCAAgC,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAGD,SAAgB,6BAA6B,CAAC,cAAsB,GAAG,EAAE,WAAmB,KAAK;IAC/F,MAAM,YAAY,GAAG,IAAI,GAAG,EAAgD,CAAC;IAE7E,OAAO,KAAK,UAAU,uBAAuB,CAC3C,OAAuB,EACvB,KAAmB;QAEnB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,GAAG,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAE5C,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE;gBACvB,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;aAC1B,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,SAAS,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC;YACnC,OAAO,yBAAc,CAAC,KAAK,CACzB,KAAK,EACL,qBAAqB,EACrB,4CAA4C,EAC5C,GAAG,CACJ,CAAC;QACJ,CAAC;QAED,SAAS,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC"}