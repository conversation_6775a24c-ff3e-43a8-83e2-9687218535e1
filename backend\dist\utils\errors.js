"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CircuitBreaker = exports.createError = exports.DatabaseError = exports.ExternalServiceError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.AppError = void 0;
exports.isOperationalError = isOperationalError;
exports.logError = logError;
exports.formatErrorResponse = formatErrorResponse;
exports.asyncHandler = asyncHandler;
exports.retry = retry;
exports.handleUncaughtException = handleUncaughtException;
exports.handleUnhandledRejection = handleUnhandledRejection;
const logger_1 = require("./logger");
class AppError extends Error {
    statusCode;
    code;
    isOperational;
    details;
    constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', isOperational = true, details) {
        super(message);
        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.code = code;
        this.isOperational = isOperational;
        this.details = details;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message, details) {
        super(message, 422, 'VALIDATION_ERROR', true, details);
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends AppError {
    constructor(message = 'Authentication required') {
        super(message, 401, 'AUTHENTICATION_ERROR', true);
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends AppError {
    constructor(message = 'Insufficient permissions') {
        super(message, 403, 'AUTHORIZATION_ERROR', true);
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends AppError {
    constructor(message = 'Resource not found') {
        super(message, 404, 'NOT_FOUND', true);
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends AppError {
    constructor(message = 'Resource conflict') {
        super(message, 409, 'CONFLICT', true);
    }
}
exports.ConflictError = ConflictError;
class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded') {
        super(message, 429, 'RATE_LIMIT_EXCEEDED', true);
    }
}
exports.RateLimitError = RateLimitError;
class ExternalServiceError extends AppError {
    constructor(service, message = 'External service error') {
        super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR', true, { service });
    }
}
exports.ExternalServiceError = ExternalServiceError;
class DatabaseError extends AppError {
    constructor(message = 'Database operation failed') {
        super(message, 500, 'DATABASE_ERROR', true);
    }
}
exports.DatabaseError = DatabaseError;
exports.createError = {
    validation: (message, details) => new ValidationError(message, details),
    authentication: (message) => new AuthenticationError(message),
    authorization: (message) => new AuthorizationError(message),
    notFound: (message) => new NotFoundError(message),
    conflict: (message) => new ConflictError(message),
    rateLimit: (message) => new RateLimitError(message),
    externalService: (service, message) => new ExternalServiceError(service, message),
    database: (message) => new DatabaseError(message),
    internal: (message, details) => new AppError(message, 500, 'INTERNAL_ERROR', true, details),
};
function isOperationalError(error) {
    if (error instanceof AppError) {
        return error.isOperational;
    }
    return false;
}
function logError(error, context) {
    const errorInfo = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        ...context,
    };
    if (error instanceof AppError) {
        errorInfo.statusCode = error.statusCode;
        errorInfo.code = error.code;
        errorInfo.isOperational = error.isOperational;
        errorInfo.details = error.details;
    }
    if (isOperationalError(error)) {
        logger_1.logger.warn('Operational error:', errorInfo);
    }
    else {
        logger_1.logger.error('System error:', errorInfo);
    }
}
function formatErrorResponse(error) {
    if (error instanceof AppError) {
        return {
            success: false,
            error: {
                code: error.code,
                message: error.message,
                details: error.details,
            },
        };
    }
    const isProduction = process.env['NODE_ENV'] === 'production';
    return {
        success: false,
        error: {
            code: 'INTERNAL_ERROR',
            message: isProduction ? 'An unexpected error occurred' : error.message,
            details: isProduction ? undefined : { stack: error.stack },
        },
    };
}
function asyncHandler(fn) {
    return async (...args) => {
        try {
            return await fn(...args);
        }
        catch (error) {
            logError(error, { function: fn.name, args });
            throw error;
        }
    };
}
class CircuitBreaker {
    threshold;
    resetTimeout;
    failures = 0;
    lastFailureTime = 0;
    state = 'CLOSED';
    constructor(threshold = 5, resetTimeout = 30000) {
        this.threshold = threshold;
        this.resetTimeout = resetTimeout;
    }
    async execute(operation) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.resetTimeout) {
                this.state = 'HALF_OPEN';
            }
            else {
                throw exports.createError.externalService('Circuit Breaker', 'Service temporarily unavailable');
            }
        }
        try {
            const result = await operation();
            this.onSuccess();
            return result;
        }
        catch (error) {
            this.onFailure();
            throw error;
        }
    }
    onSuccess() {
        this.failures = 0;
        this.state = 'CLOSED';
    }
    onFailure() {
        this.failures++;
        this.lastFailureTime = Date.now();
        if (this.failures >= this.threshold) {
            this.state = 'OPEN';
        }
    }
    getState() {
        return this.state;
    }
    getFailures() {
        return this.failures;
    }
}
exports.CircuitBreaker = CircuitBreaker;
async function retry(operation, maxAttempts = 3, delay = 1000, backoff = 2) {
    let lastError;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await operation();
        }
        catch (error) {
            lastError = error;
            if (attempt === maxAttempts) {
                break;
            }
            if (error instanceof AppError && error.statusCode >= 400 && error.statusCode < 500) {
                break;
            }
            const waitTime = delay * Math.pow(backoff, attempt - 1);
            logger_1.logger.warn(`Operation failed, retrying in ${waitTime}ms (attempt ${attempt}/${maxAttempts}):`, {
                error: error instanceof Error ? error.message : String(error),
            });
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
    }
    throw lastError;
}
function handleUncaughtException(error) {
    logger_1.logger.fatal('Uncaught exception:', {
        error: error.message,
        stack: error.stack,
    });
    process.exit(1);
}
function handleUnhandledRejection(reason, _promise) {
    logger_1.logger.fatal('Unhandled promise rejection:', {
        reason: reason instanceof Error ? reason.message : String(reason),
        stack: reason instanceof Error ? reason.stack : undefined,
    });
    process.exit(1);
}
//# sourceMappingURL=errors.js.map