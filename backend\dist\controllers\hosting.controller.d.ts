import { FastifyRequest, FastifyReply } from 'fastify';
export interface HostingPlan {
    tier: 'Shared' | 'Dedicated' | 'Enterprise';
    plan_name: string;
    description: string;
    features: string[];
    cpu_allocation?: number;
    memory_allocation?: number;
    storage_allocation?: number;
    bandwidth_limit?: number;
    max_concurrent_users?: number;
    price_per_hour: number;
    price_per_month: number;
    vultr_plan?: string;
    recommended_for: string[];
    african_pricing?: {
        nigeria_naira: number;
        south_africa_rand: number;
        kenya_shilling: number;
        ghana_cedi: number;
    };
    free_tier?: boolean;
    trial_days?: number;
}
export declare function getHostingPlansController(request: FastifyRequest<{
    Querystring: {
        tier?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getHostingPlanController(request: FastifyRequest<{
    Params: {
        planName: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getRecommendedPlanController(request: FastifyRequest<{
    Querystring: {
        service_type?: string;
        expected_traffic?: string;
        budget?: string;
        tier?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function createSharedUserController(request: FastifyRequest<{
    Body: {
        user_id: string;
        username: string;
        plan: string;
        server_id?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getSharedUsersController(request: FastifyRequest<{
    Querystring: {
        page?: string;
        limit?: string;
        status?: 'active' | 'suspended' | 'inactive';
        plan?: string;
        server_id?: string;
        search?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getSharedUserController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function updateSharedUserController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Body: {
        plan?: string;
        status?: 'active' | 'suspended' | 'inactive';
        resource_limits?: {
            cpu_quota?: number;
            memory_max?: number;
            bandwidth_limit?: number;
            storage_limit?: number;
        };
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function deleteSharedUserController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getSharedHostingAnalyticsController(request: FastifyRequest<{
    Querystring: {
        period?: 'day' | 'week' | 'month' | 'year';
        server_id?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getUserResourceUsageController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Querystring: {
        period?: 'day' | 'week' | 'month';
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getServerCapacityController(request: FastifyRequest<{
    Params: {
        serverId?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getUserApplicationsController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Querystring: {
        page?: string;
        limit?: string;
        status?: 'running' | 'stopped' | 'building' | 'failed';
        type?: 'static-website' | 'web-service';
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function createApplicationController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Body: {
        name: string;
        type: 'static-website' | 'web-service';
        framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
        domain?: string;
        git_repo?: string;
        build_command?: string;
        start_command?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function updateUserApplicationController(request: FastifyRequest<{
    Params: {
        userId: string;
        appId: string;
    };
    Body: {
        name?: string;
        domain?: string;
        git_repo?: string;
        build_command?: string;
        start_command?: string;
        status?: 'running' | 'stopped';
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function deleteUserApplicationController(request: FastifyRequest<{
    Params: {
        userId: string;
        appId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function suspendUserController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Body: {
        reason?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function reactivateUserController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function resetUserPasswordController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Body: {
        new_password?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
//# sourceMappingURL=hosting.controller.d.ts.map