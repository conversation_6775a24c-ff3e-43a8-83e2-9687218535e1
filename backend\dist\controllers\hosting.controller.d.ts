import { FastifyRequest, FastifyReply } from 'fastify';
export interface HostingPlan {
    tier: 'Shared' | 'Dedicated' | 'Enterprise';
    plan_name: string;
    description: string;
    features: string[];
    cpu_allocation?: number;
    memory_allocation?: number;
    storage_allocation?: number;
    bandwidth_limit?: number;
    max_concurrent_users?: number;
    price_per_hour: number;
    price_per_month: number;
    vultr_plan?: string;
    recommended_for: string[];
    african_pricing?: {
        nigeria_naira: number;
        south_africa_rand: number;
        kenya_shilling: number;
        ghana_cedi: number;
    };
    free_tier?: boolean;
    trial_days?: number;
}
export declare function getHostingPlansController(request: FastifyRequest<{
    Querystring: {
        tier?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getHostingPlanController(request: FastifyRequest<{
    Params: {
        planName: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getRecommendedPlanController(request: FastifyRequest<{
    Querystring: {
        service_type?: string;
        expected_traffic?: string;
        budget?: string;
        tier?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
//# sourceMappingURL=hosting.controller.d.ts.map