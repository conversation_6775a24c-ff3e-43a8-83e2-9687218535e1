import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { appConfig } from '../config';
import { logger } from '../utils/logger';

export interface VultrServer {
  id: string;
  os: string;
  ram: number;
  disk: number;
  main_ip: string;
  vcpu_count: number;
  region: string;
  plan: string;
  date_created: string;
  status: string;
  allowed_bandwidth: number;
  netmask_v4: string;
  gateway_v4: string;
  power_status: string;
  server_status: string;
  v6_network: string;
  v6_main_ip: string;
  v6_network_size: number;
  label: string;
  internal_ip: string;
  kvm: string;
  hostname: string;
  tag: string;
  os_id: number;
  app_id: number;
  image_id: string;
  firewall_group_id: string;
  features: string[];
  user_data: string;
  default_password: string;
}

export interface VultrPlan {
  id: string;
  vcpu_count: number;
  ram: number;
  disk: number;
  bandwidth: number;
  monthly_cost: number;
  type: string;
  locations: string[];
  disk_count: number;
}

export interface VultrRegion {
  id: string;
  city: string;
  country: string;
  continent: string;
  options: string[];
}

export interface VultrBilling {
  balance: number;
  pending_charges: number;
  last_payment_date: string;
  last_payment_amount: number;
}

export class VultrService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: appConfig.vultr.baseUrl,
      headers: {
        'Authorization': `Bearer ${appConfig.vultr.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`Vultr API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('Vultr API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`Vultr API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('Vultr API Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          url: error.config?.url,
        });
        return Promise.reject(error);
      }
    );
  }

  // Server management methods
  async getServers(): Promise<VultrServer[]> {
    try {
      const response: AxiosResponse<{ instances: VultrServer[] }> = await this.client.get('/instances');
      return response.data.instances || [];
    } catch (error) {
      logger.error('Failed to get Vultr servers:', error);
      throw new Error('Failed to fetch servers from Vultr');
    }
  }

  async getServer(serverId: string): Promise<VultrServer> {
    try {
      const response: AxiosResponse<{ instance: VultrServer }> = await this.client.get(`/instances/${serverId}`);
      return response.data.instance;
    } catch (error) {
      logger.error(`Failed to get Vultr server ${serverId}:`, error);
      throw new Error('Failed to fetch server from Vultr');
    }
  }

  async createServer(serverData: any): Promise<VultrServer> {
    try {
      const response: AxiosResponse<{ instance: VultrServer }> = await this.client.post('/instances', serverData);
      return response.data.instance;
    } catch (error) {
      logger.error('Failed to create Vultr server:', error);
      throw new Error('Failed to create server on Vultr');
    }
  }

  async deleteServer(serverId: string): Promise<void> {
    try {
      await this.client.delete(`/instances/${serverId}`);
    } catch (error) {
      logger.error(`Failed to delete Vultr server ${serverId}:`, error);
      throw new Error('Failed to delete server on Vultr');
    }
  }

  async startServer(serverId: string): Promise<void> {
    try {
      await this.client.post(`/instances/${serverId}/start`);
    } catch (error) {
      logger.error(`Failed to start Vultr server ${serverId}:`, error);
      throw new Error('Failed to start server on Vultr');
    }
  }

  async stopServer(serverId: string): Promise<void> {
    try {
      await this.client.post(`/instances/${serverId}/halt`);
    } catch (error) {
      logger.error(`Failed to stop Vultr server ${serverId}:`, error);
      throw new Error('Failed to stop server on Vultr');
    }
  }

  async rebootServer(serverId: string): Promise<void> {
    try {
      await this.client.post(`/instances/${serverId}/reboot`);
    } catch (error) {
      logger.error(`Failed to reboot Vultr server ${serverId}:`, error);
      throw new Error('Failed to reboot server on Vultr');
    }
  }

  // Plans and regions
  async getPlans(): Promise<VultrPlan[]> {
    try {
      const response: AxiosResponse<{ plans: VultrPlan[] }> = await this.client.get('/plans');
      return response.data.plans || [];
    } catch (error) {
      logger.error('Failed to get Vultr plans:', error);
      throw new Error('Failed to fetch plans from Vultr');
    }
  }

  async getRegions(): Promise<VultrRegion[]> {
    try {
      const response: AxiosResponse<{ regions: VultrRegion[] }> = await this.client.get('/regions');
      return response.data.regions || [];
    } catch (error) {
      logger.error('Failed to get Vultr regions:', error);
      throw new Error('Failed to fetch regions from Vultr');
    }
  }

  // Billing information
  async getBilling(): Promise<VultrBilling> {
    try {
      const response: AxiosResponse<{ billing: VultrBilling }> = await this.client.get('/account');
      return response.data.billing;
    } catch (error) {
      logger.error('Failed to get Vultr billing:', error);
      throw new Error('Failed to fetch billing information from Vultr');
    }
  }

  // Block storage
  async getBlockStorage(): Promise<any[]> {
    try {
      const response: AxiosResponse<{ blocks: any[] }> = await this.client.get('/blocks');
      return response.data.blocks || [];
    } catch (error) {
      logger.error('Failed to get Vultr block storage:', error);
      throw new Error('Failed to fetch block storage from Vultr');
    }
  }

  async createBlockStorage(storageData: any): Promise<any> {
    try {
      const response: AxiosResponse<{ block: any }> = await this.client.post('/blocks', storageData);
      return response.data.block;
    } catch (error) {
      logger.error('Failed to create Vultr block storage:', error);
      throw new Error('Failed to create block storage on Vultr');
    }
  }

  async deleteBlockStorage(blockId: string): Promise<void> {
    try {
      await this.client.delete(`/blocks/${blockId}`);
    } catch (error) {
      logger.error(`Failed to delete Vultr block storage ${blockId}:`, error);
      throw new Error('Failed to delete block storage on Vultr');
    }
  }

  async attachBlockStorage(blockId: string, instanceId: string): Promise<void> {
    try {
      await this.client.post(`/blocks/${blockId}/attach`, { instance_id: instanceId });
    } catch (error) {
      logger.error(`Failed to attach block storage ${blockId} to ${instanceId}:`, error);
      throw new Error('Failed to attach block storage on Vultr');
    }
  }

  async detachBlockStorage(blockId: string): Promise<void> {
    try {
      await this.client.post(`/blocks/${blockId}/detach`);
    } catch (error) {
      logger.error(`Failed to detach block storage ${blockId}:`, error);
      throw new Error('Failed to detach block storage on Vultr');
    }
  }

  // Account information
  async getAccountInfo(): Promise<any> {
    try {
      const response: AxiosResponse<{ account: any }> = await this.client.get('/account');
      return response.data.account;
    } catch (error) {
      logger.error('Failed to get Vultr account info:', error);
      throw new Error('Failed to fetch account information from Vultr');
    }
  }

  // Operating systems
  async getOperatingSystems(): Promise<any[]> {
    try {
      const response: AxiosResponse<{ os: any[] }> = await this.client.get('/os');
      return response.data.os || [];
    } catch (error) {
      logger.error('Failed to get Vultr operating systems:', error);
      throw new Error('Failed to fetch operating systems from Vultr');
    }
  }
}

  // SSH Keys
  async getSSHKeys(): Promise<any[]> {
    try {
      const response: AxiosResponse<{ ssh_keys: any[] }> = await this.client.get('/ssh-keys');
      return response.data.ssh_keys || [];
    } catch (error) {
      logger.error('Failed to get Vultr SSH keys:', error);
      throw new Error('Failed to fetch SSH keys from Vultr');
    }
  }

  // Snapshots
  async getSnapshots(): Promise<any[]> {
    try {
      const response: AxiosResponse<{ snapshots: any[] }> = await this.client.get('/snapshots');
      return response.data.snapshots || [];
    } catch (error) {
      logger.error('Failed to get Vultr snapshots:', error);
      throw new Error('Failed to fetch snapshots from Vultr');
    }
  }

  // Load balancers
  async getLoadBalancers(): Promise<any[]> {
    try {
      const response: AxiosResponse<{ load_balancers: any[] }> = await this.client.get('/load-balancers');
      return response.data.load_balancers || [];
    } catch (error) {
      logger.error('Failed to get Vultr load balancers:', error);
      throw new Error('Failed to fetch load balancers from Vultr');
    }
  }
}

// Singleton instance
export const vultrService = new VultrService();
